using Notify.Options;
using Volo.Abp.Modularity;
using Volo.Abp.Users;

namespace Notify.Provider.Email;


[DependsOn(
    typeof(NotifyDomainModule),
    typeof(AbpUsersAbstractionModule)
)]
public class NotifyProviderEmailModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<NotifyOptions>(options =>
        {
            options.Providers.AddProvider(new NotifyProviderConfiguration(
                NotifyProviderEmailConsts.NotificationMethodEmail, typeof(EmailNotificationManager)));
        });
    }
}
