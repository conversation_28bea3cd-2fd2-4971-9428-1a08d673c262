using Notify.Notifications.UserNotifications;
using Notify.Notifications;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Uow;

namespace Notify.Provider.Email;

public class EmailNotificationSendingJob : IAsyncBackgroundJob<EmailNotificationSendingJobArgs>, ITransientDependency
{
    private readonly INotificationRepository _notificationRepository;
    private readonly EmailNotificationManager _emailNotificationManager;
    private readonly IUserNotificationRepository _userNotificationRepository;

    public EmailNotificationSendingJob(
        INotificationRepository notificationRepository,
        EmailNotificationManager emailNotificationManager,
        IUserNotificationRepository userNotificationRepository)
    {
        _notificationRepository = notificationRepository;
        _userNotificationRepository = userNotificationRepository;
        _emailNotificationManager = emailNotificationManager;
    }

    [UnitOfWork]
    public async Task ExecuteAsync(EmailNotificationSendingJobArgs args)
    {
        var userNotification = await _userNotificationRepository.GetAsync(args.UserNotificationId);
        var notification = await _notificationRepository.GetAsync(userNotification.NotificationId);

        await _emailNotificationManager.SendUserNotificationAsync(userNotification, notification);
    }
}
