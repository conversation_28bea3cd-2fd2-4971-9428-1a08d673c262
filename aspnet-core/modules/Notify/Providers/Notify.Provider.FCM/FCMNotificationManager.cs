using Microsoft.Extensions.Logging;
using Notify.Notifications;
using Notify.Notifications.UserNotifications;
using Volo.Abp.ExceptionHandling;
using Volo.Abp.Uow;

namespace Notify.Provider.FCM;

public class FCMNotificationManager : NotificationManagerBase
{
    protected override string NotificationMethod => NotifyProviderFCMConsts.NotificationMethodFCM;

    private IFCMSender FCMSender => LazyServiceProvider.LazyGetRequiredService<IFCMSender>();

    private IUserDeviceTokenProvider UserDeviceTokenProvider =>
        LazyServiceProvider.LazyGetRequiredService<IUserDeviceTokenProvider>();


    [UnitOfWork(true)]
    public override async Task<(List<UserNotification> UserNotifications, Notification Notification)> CreateAsync(CreateNotificationModel model)
    {
        var notification = new Notification(GuidGenerator.Create());

        notification.SetFCMTitle(model.GetTitle());
        notification.SetFCMBody(model.GetBody());

        var userNotifications = await base.CreateUserNotificationsAsync(notification, model);

        return (userNotifications, notification);
    }

    [UnitOfWork]
    public override async Task SendUserNotificationAsync(UserNotification userNotification, Notification notification)
    {
        var userDeviceTokens = await UserDeviceTokenProvider.GetListAsync(userNotification.UserId);

        if (userDeviceTokens.IsNullOrEmpty())
        {
            userNotification.SetResult(Clock, false, NotifyProviderFCMConsts.FailureReasonWhenDeviceTokenNotFound);
            await UserNotificationRepository.UpdateAsync(userNotification);

            return;
        }

        var fCMMessage = new FCMMessage(userDeviceTokens, title: notification.GetFCMTitle(), body: notification.GetFCMBody());

        try
        {
            var faildTokens = await FCMSender.SendAsync(fCMMessage);

            if (faildTokens.Any())
            {
                userNotification.SetResult(Clock, false, NotifyProviderFCMConsts.FailureReasonWhenFirebasethrowsException);

                await UserNotificationRepository.UpdateAsync(userNotification);

                return;
            }

            userNotification.SetResult(Clock, true);

            await UserNotificationRepository.UpdateAsync(userNotification);
        }
        catch (Exception e)
        {
            Logger.LogException(e);

            var message = e is IHasErrorCode b ? b.Code ?? e.Message : e.ToString();
            userNotification.SetResult(Clock, false, message);

            await UserNotificationRepository.UpdateAsync(userNotification);
        }
    }
}
