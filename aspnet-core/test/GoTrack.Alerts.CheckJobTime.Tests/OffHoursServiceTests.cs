using System;
using System.Collections.Generic;
using System.Linq;
using GoTrack.Alerts.CheckJobTime;
using Xunit;

namespace GoTrack;

public class OffHoursServiceTests
{
    private readonly OffHoursService _service = new();

    [Fact]
    public void ReturnsSingleInterval_WhenOffHoursSpanAcrossMidnightOnWorkingDay()
    {
        // Covers Saturday 21:00 -> Sunday 00:00 UTC
        var start = DateTimeOffset.Parse("2025-07-11T21:00:00Z")
            .ToUnixTimeMilliseconds().ToString();
        var end = DateTimeOffset.Parse("2025-07-12T00:00:00Z")
            .ToUnixTimeMilliseconds().ToString();

        var intervals = _service.GetNonWorkingIntervals(
            start, end,
            TimeOnly.Parse("01:00"), TimeOnly.Parse("23:00"),
            new List<DayOfWeek> { DayOfWeek.Saturday }
        );

        // Only off-hour segment is 21:00->22:00 UTC (Saturday 00:00+3 -> 22:00 UTC)
        Assert.Single(intervals);
        Assert.Equal(start, intervals.First().From);
        Assert.Equal(
            DateTimeOffset.Parse("2025-07-11T22:00:00Z")
                .ToUnixTimeMilliseconds().ToString(),
            intervals.First().To);
    }

    [Fact]
    public void Boundary_StartEqualsWorkStart_ProducesAfterWorkIntervalOnly()
    {
        // Saturday work window 01:00-23:00 UTC+3 => 22:00-20:00 UTC
        var date = "2025-07-12"; // Saturday UTC+3
        var start = DateTimeOffset.Parse("2025-07-12T22:00:00Z")
            .ToUnixTimeMilliseconds().ToString();
        var end = DateTimeOffset.Parse("2025-07-13T00:00:00Z")
            .ToUnixTimeMilliseconds().ToString();

        var intervals = _service.GetNonWorkingIntervals(
            start, end,
            TimeOnly.Parse("01:00"), TimeOnly.Parse("23:00"),
            new List<DayOfWeek> { DayOfWeek.Saturday }
        );

        // No off before (start on boundary), only from end of work at 20:00 UTC -> here start=22:00, so interval 22:00->24:00
        Assert.Single(intervals);
        Assert.Equal(start, intervals[0].From);
        Assert.Equal(
            DateTimeOffset.Parse("2025-07-13T00:00:00Z")
                .ToUnixTimeMilliseconds().ToString(),
            intervals[0].To);
    }

    [Fact]
    public void Boundary_EndEqualsWorkEnd_ProducesBeforeWorkIntervalOnly()
    {
        // Saturday work window 01:00-23:00 UTC+3 => 22:00(Friday)-20:00(Saturday) UTC
        var start = DateTimeOffset.Parse("2025-07-12T00:00:00Z") // Saturday
            .ToUnixTimeMilliseconds().ToString();
        var end = DateTimeOffset.Parse("2025-07-12T20:00:00Z") // Saturday
            .ToUnixTimeMilliseconds().ToString();

        var intervals = _service.GetNonWorkingIntervals(
            start, end,
            TimeOnly.Parse("01:00"), TimeOnly.Parse("23:00"),
            new List<DayOfWeek> { DayOfWeek.Saturday }
        );

        Assert.Empty(intervals);
    }
    
    
    [Fact]
    public void SamyTest()
    {
        var start = DateTimeOffset.Parse("2025-01-05T03:00:00Z") // 
            .ToUnixTimeMilliseconds().ToString();
        var end = DateTimeOffset.Parse("2025-01-12T03:00:00Z") // 
            .ToUnixTimeMilliseconds().ToString();

        var intervals = _service.GetNonWorkingIntervals(
            start, end,
            TimeOnly.Parse("10:00"), TimeOnly.Parse("15:00"),
            new List<DayOfWeek> { DayOfWeek.Monday, DayOfWeek.Wednesday,DayOfWeek.Thursday, }
        );

        Assert.Empty(intervals);
    }
    
    [Fact]
    public void SamyTest2()
    {
        var start = DateTimeOffset.Parse("2025-01-06T13:00:00Z") // 
            .ToUnixTimeMilliseconds().ToString();
        var end = DateTimeOffset.Parse("2025-01-12T03:00:00Z") // 
            .ToUnixTimeMilliseconds().ToString();

        var intervals = _service.GetNonWorkingIntervals(
            start, end,
            TimeOnly.Parse("10:00"), TimeOnly.Parse("15:00"),
            new List<DayOfWeek> { DayOfWeek.Monday, DayOfWeek.Wednesday,DayOfWeek.Thursday, }
        );

        Assert.Empty(intervals);
    }

    [Fact]
    public void InvalidTimestamp_ThrowsException()
    {
        Assert.Throws<ArgumentException>(() =>
            _service.GetNonWorkingIntervals("abc", "123", TimeOnly.MinValue, TimeOnly.MaxValue, new List<DayOfWeek>()));
    }

    [Fact]
    public void EndBeforeStart_ThrowsException()
    {
        var now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        var past = DateTimeOffset.UtcNow.AddHours(-1).ToUnixTimeMilliseconds().ToString();
        Assert.Throws<ArgumentException>(() =>
            _service.GetNonWorkingIntervals(now, past, TimeOnly.MinValue, TimeOnly.MaxValue, new List<DayOfWeek>()));
    }
}