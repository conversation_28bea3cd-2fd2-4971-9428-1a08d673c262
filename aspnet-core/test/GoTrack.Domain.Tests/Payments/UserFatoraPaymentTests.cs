using Xunit;
using Microsoft.Extensions.Logging;
using System;
using GoTrack.Requests;
using Volo.Abp;

namespace GoTrack.Payments;

public class UserFatoraPaymentTests : GoTrackDomainTestBase
{
    private readonly Guid _userId = Guid.NewGuid();
    private readonly Guid _requestId = Guid.NewGuid();
    private readonly int _amount = 100;
    private readonly string _callBackUrl = "https://example.com/callback";
    private readonly string _notes = "Test Notes";
    private readonly uint _terminalId = 12345;
    private readonly PaymentStatus _paymentStatus = PaymentStatus.Pending;
    private readonly RequestType _requestType = RequestType.BusinessAccountSubscription;

    private UserFatoraPayment CreateUserFatoraPayment()
    {
        return new UserFatoraPayment(
            Guid.NewGuid(),
            _userId,
            _requestId,
            _amount,
            _callBackUrl,
            _notes,
            _terminalId,
            _paymentStatus,
            _requestType
        );
    }

    [Fact]
    public void Should_Set_Rrn_And_PaymentStatus_Successfully()
    {
        // Arrange
        var payment = CreateUserFatoraPayment();
        var loggerMock = GetRequiredService<ILogger>();
        var rrn = "*********";
        var paymentStatus = "Success";

        // Act
        payment.SetRrnAndPaymentStatus(rrn, paymentStatus, loggerMock);

        // Assert
        Assert.Equal(rrn, payment.Rrn);
        Assert.Equal(PaymentStatus.Success, payment.PaymentStatus);
    }

    [Fact]
    public void Should_Throw_Exception_When_Rrn_Is_Already_Set()
    {
        // Arrange
        var payment = CreateUserFatoraPayment();
        var loggerMock = GetRequiredService<ILogger>();
        payment.SetRrnAndPaymentStatus("*********", "Success", loggerMock);

        // Act & Assert
        Assert.Throws<BusinessException>(() =>
            payment.SetRrnAndPaymentStatus("987654321", "Failed", loggerMock)
        );
    }

    [Fact]
    public void Should_Throw_Exception_When_PaymentStatus_Is_Already_Set()
    {
        // Arrange
        var payment = CreateUserFatoraPayment();
        var loggerMock = GetRequiredService<ILogger>();
        payment.SetRrnAndPaymentStatus("*********", "Success", loggerMock);

        // Act & Assert
        Assert.Throws<BusinessException>(() =>
            payment.SetRrnAndPaymentStatus("*********", "Success", loggerMock)
        );
    }

    [Fact]
    public void Should_Throw_Exception_When_Invalid_PaymentStatus()
    {
        // Arrange
        var payment = CreateUserFatoraPayment();
        var loggerMock = GetRequiredService<ILogger>();
        var rrn = "*********";
        var invalidStatus = "InvalidStatus";

        // Act & Assert
        Assert.Throws<BusinessException>(() =>
            payment.SetRrnAndPaymentStatus(rrn, invalidStatus, loggerMock)
        );
    }
}
