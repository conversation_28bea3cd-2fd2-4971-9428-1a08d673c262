using System.Threading.Tasks;
using GoTrack.Mobile.MobileIdentityUsers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Content;

namespace GoTrack.Mobile.Controllers;

[ApiController]
[Route("api/app/[controller]/[action]")]
public class MobileIdentityUserController : GoTrackMobileController
{
    [HttpPost]
    public async Task<IActionResult> SetProfilePictureAsync(
        [FromServices] IMobileIdentityUserAppService userAppService,
        IFormFile profilePictureFile)
    {
        await userAppService.SetProfilePictureAsync(new RemoteStreamContent(profilePictureFile.OpenReadStream(),
            profilePictureFile.FileName, profilePictureFile.ContentType, profilePictureFile.Length));
        return Ok();
    }
}