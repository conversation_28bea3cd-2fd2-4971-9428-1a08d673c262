using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Alerts.BaseChecker;
using GoTrack.Alerts.BaseChecker.Models;
using GoTrack.Alerts.CheckExternalPowerCutOff.Models;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using Warp10Abstraction;

namespace GoTrack.Alerts.CheckExternalPowerCutOff;

public class RepoService : IRepoService
{
    private readonly GoTrackAlertCheckExternalPowerCutOffContext _dbContext;

    public RepoService(GoTrackAlertCheckExternalPowerCutOffContext dbContext)
    {
        _dbContext = dbContext;
    }

    public GroupedAlertListBase[] GetGroupedAlertsList(int pageNumber, int pageSize)
    {
        return _dbContext.GroupedAlertLists
            .Skip(pageNumber * pageSize)
            .Take(pageSize)
            .ToArray<GroupedAlertListBase>();
    }

    public int GetGroupedAlertsListCount()
    {
        return _dbContext.GroupedAlertLists.Count();
    }

    public AlertBase[] GetAlertsByIds(string[] ids)
    {
        return _dbContext.AlertLists
            .Where(x => ids.Contains(x.Id.ToString()))
            .ToArray<AlertBase>();
    }

    public AlertBase GetAlert(Guid evId)
    {
        return _dbContext.AlertLists
            .FirstOrDefault(x => x.Id == evId);
    }

    public void SaveUpdatedAlert()
    {
        _dbContext.SaveChanges();
    }

    public async Task ParseAndSaveAlert(AlertCrudToService alert)
    {
        AlertCrud alertCrud = alert.AlertCrud;

        var alertList = (AlertList)GetAlert(alertCrud.Id);

        if (alertCrud.CrudType == CrudType.Update && alertList is null)
        {
            throw new Exception("Update operation to non existing alert, AlertId: " + alertCrud.Id);
        }

        if (alertCrud.CrudType == CrudType.Add)
        {
            alertList = new AlertList()
            {
                Id = alertCrud.Id,
                AffectiveFrom = alertCrud.AffectiveFrom,
                LastCheckedAt = WarpHelper.ConvertDate(alertCrud.AffectiveFrom).ToString(),
            };
        }

        if (alertCrud is ExternalPowerCutOffAlertCrud externalPowerCutOffAlert)
        {
            alertList.Imei = externalPowerCutOffAlert.Imei;
        }

        if (alertCrud.CrudType == CrudType.Add)
        {
            _dbContext.AlertLists.Add(alertList);
        }

        if (alertCrud.CrudType == CrudType.Update)
        {
            _dbContext.AlertLists.Update(alertList);
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteAlert(AlertBase alertBase)
    {
        _dbContext.AlertLists.Remove((AlertList)alertBase);

        await _dbContext.SaveChangesAsync();
    }
}