using System;
using System.Text.Json.Serialization;
using GoTrack.Alerts.Contracts.Exchanges.Enums;

namespace GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;

[JsonDerivedType(typeof(OverSpeedAlertCrud), "OverSpeed")]
[JsonDerivedType(typeof(StopInZoneAlertCrud), "StopInZone")]
[JsonDerivedType(typeof(ZoneInOutAlertCrud), "ZoneInOutAlert")]
[JsonDerivedType(typeof(JobTimeAlertCrud), "JobTime")]
public abstract class AlertCrud
{
    [JsonConstructor]
    protected AlertCrud() { }
    public CrudType CrudType { get; set; }
    public Guid Id { get; set; }
    public AlertCode Code { get; set; }
    public DateTime AffectiveFrom { get; set; }
}