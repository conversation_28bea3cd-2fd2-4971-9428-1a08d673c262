using System.Net.Http.Headers;
using System.Text;
using Fatora.Abstractions;
using Fatora.Abstractions.DTO;
using Fatora.Abstractions.DTO.CreatePaymentDtos;
using Fatora.Abstractions.DTO.PaymentStatusDtos;
using Fatora.Abstractions.DTO.RevesalPaymentDto;
using Fatora.Abstractions.Exceptions;
using Fatora.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Fatora;

public class FatoraService : IFatoraService
{
    private readonly ILogger<FatoraService> _logger;
    private readonly FatoraOptions _fatoraOptions;
    private readonly HttpClient _httpClient;

    public FatoraService(ILogger<FatoraService> logger, IOptions<FatoraOptions> fatoraOptions)
    {
        _logger = logger;
        _fatoraOptions = fatoraOptions.Value;

        _httpClient = new HttpClient
        {
            BaseAddress = new Uri(_fatoraOptions.Url)
        };

        var byteArray = Encoding.ASCII.GetBytes($"{_fatoraOptions.UserName}:{_fatoraOptions.Password}");
        
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
    }

    public async Task<CreatePaymentResponseData> CreatePaymentAsync(CreatePaymentRequestDto createPaymentRequestDto, CancellationToken cancellationToken = default)
    {
        const string requestUrl = "create-payment";

        try
        {
            var requestBody = JsonConvert.SerializeObject(new
            {
                lang = createPaymentRequestDto.Language,
                amount = createPaymentRequestDto.Amount,
                callbackURL = createPaymentRequestDto.CallBackUrl,
                triggerURL = createPaymentRequestDto.TriggerUrl,
                savedCards = createPaymentRequestDto.SavedCards,
                appUser = createPaymentRequestDto.UserIdentifier,
                notes = createPaymentRequestDto.Notes,
                terminalId = _fatoraOptions.TerminalId
            });

            var request = new HttpRequestMessage(HttpMethod.Post, string.Concat(_httpClient.BaseAddress, requestUrl))
            {
                Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request, cancellationToken);
            string stringResponse = "";

            if (response.IsSuccessStatusCode)
            {
                stringResponse = await response.Content.ReadAsStringAsync(cancellationToken);

                var result = JsonConvert.DeserializeObject<CreatePaymentResponseDto>(stringResponse);
                if (result is not null && result.ErrorCode == 0)
                {
                    return result.Data;
                }
            }

            throw new FatoraException("error in integration with Fatora api. the response is : " + stringResponse);
        }
        catch (HttpRequestException)
        {
            _logger.LogError("Fatora server connection field");
            throw new FatoraException("Fatora server connection field");
        }
        catch (FatoraException e)
        {
            _logger.LogError(e.Message);
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError("Error Occured \n{ErrorMessage}", e.Message);
            throw new FatoraException(e.Message, e.StackTrace);
        }

    }

    public async Task<GetPaymentStatusResponseData> GetPaymentStatus(Guid paymentId, CancellationToken cancellationToken = default)
    {
        const string requestUrl = "get-payment-status/";

        try
        {
            var request = new HttpRequestMessage(HttpMethod.Get,
                string.Concat(_httpClient.BaseAddress, requestUrl + paymentId));

            var response = await _httpClient.SendAsync(request, cancellationToken);
            var stringResponse = "";

            if (response.IsSuccessStatusCode)
            {
                stringResponse = await response.Content.ReadAsStringAsync(cancellationToken);

                var result = JsonConvert.DeserializeObject<GetPaymentStatusResponseDto>(stringResponse);
                if (result is not null && result.ErrorCode == 0)
                {
                    return result.Data;
                }
            }

            throw new FatoraException("error in integration with Fatora api. the response is : " + stringResponse);
        }
        catch (HttpRequestException)
        {
            _logger.LogError("Fatora server connection field");
            throw new FatoraException("Fatora server connection field");
        }
        catch (FatoraException e)
        {
            _logger.LogError(e.Message);
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError("Error Occured \n{ErrorMessage}", e.Message);
            throw new FatoraException(e.Message, e.StackTrace);
        }

    }

    public async Task ReversalPayment(ReversalPaymentRequestDto reversalPaymentRequestDto, CancellationToken cancellationToken = default)
    {
        const string requestUrl = "cancel-payment";

        try
        {
            var requestBody = JsonConvert.SerializeObject(new
            {
                lang = reversalPaymentRequestDto.Language,
                payment_id = reversalPaymentRequestDto.PaymentId
            });

            var request = new HttpRequestMessage(HttpMethod.Post, string.Concat(_httpClient.BaseAddress, requestUrl))
            {
                Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request, cancellationToken);
            var stringResponse = "";

            if (response.IsSuccessStatusCode)
            {
                stringResponse = await response.Content.ReadAsStringAsync(cancellationToken);

                var result = JsonConvert.DeserializeObject<FatoraBaseResponse>(stringResponse);
                if (result is not null && result.ErrorCode == 0)
                {
                    return;
                }
            }

            throw new FatoraException("error in integration with Fatora api. the response is : " + stringResponse);
        }
        catch (HttpRequestException)
        {
            _logger.LogError("Fatora server connection field");
            throw new FatoraException("Fatora server connection field");
        }
        catch (FatoraException e)
        {
            _logger.LogError(e.Message);
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError("Error Occured \n{ErrorMessage}", e.Message);
            throw new FatoraException(e.Message, e.StackTrace);
        }
    }
}