using GoTrack.Mobile.AlertDefinitions.ExceedingSpeedAlertDefinitions.DTOs;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.AlertDefinitions.ExceedingSpeedAlertDefinitions;

public interface IExceedingSpeedAlertDefinitionAppService : IApplicationService
{
    Task CreateAsync(CreateExceedingSpeedAlertDefinitionDto input);
    Task<ExceedingSpeedAlertDefinitionDto> GetAsync(Guid id);
    Task<PagedResultDto<ExceedingSpeedAlertDefinitionDto>> GetListAsync(PagedResultRequestDto input);
}
