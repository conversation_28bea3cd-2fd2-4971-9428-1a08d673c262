using GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.DTOs;
using GoTrack.Mobile.GeoZones;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;

public interface IEnteringZoneAlertDefinitionAppService : IApplicationService
{
    Task CreateAsync(CreateEnteringZoneAlertDefinitionDto input);
    Task<EnteringZoneAlertDefinitionDto> GetAsync(Guid id);
    Task<PagedResultDto<GeoZoneDetailsDto>> GetGeoZonesAsync(Guid id, PagedResultRequestDto input);
    Task<PagedResultDto<EnteringZoneAlertDefinitionDto>> GetListAsync(PagedResultRequestDto input);
}
