using GoTrack.Mobile.SubscriptionPlans.DTOs;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.FeatureManagement;

namespace GoTrack.Mobile.SubscriptionPlans;

public interface ISubscriptionPlanAppService : IApplicationService
{
    Task<List<FeatureDto>> GetFeaturesAsync([Required] string subscriptionPlan);
    Task<List<SubscriptionPlanDto>> GetListAsync();
}
