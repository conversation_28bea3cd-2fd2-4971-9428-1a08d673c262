using System.Collections.Generic;
using System;
using GoTrack.Mobile.Coordinates.DTOs;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.Routes.DTOs;

public class RouteDto : FullAuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public List<CoordinateDto> Line { get; set; } = new();
    public string Color { get; set; } = string.Empty;
    public CoordinateDto StartPoint { get; set; }
    public CoordinateDto EndPoint { get; set; }
    public Guid TrackAccountId { get; set; }
}
