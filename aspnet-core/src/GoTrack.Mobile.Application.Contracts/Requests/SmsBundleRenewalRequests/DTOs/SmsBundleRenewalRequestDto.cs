using System;
using GoTrack.Requests;

namespace GoTrack.Mobile.Requests.SmsBundleRenewalRequests.DTOs;

public class SmsBundleRenewalRequestDto: RequestDto
{
    public Guid OwnerId { get; set; }
    public string OwnerFirstName { get; set; } = string.Empty;
    public string OwnerLastName { get; set; } = string.Empty;
    public string OwnerEmail { get; set; } = string.Empty;
    public string OwnerPhoneNumber { get; set; } = string.Empty;
    public Guid TrackAccountSubscriptionId { get;  set; }
    public SmsBundleRenewalStage Stage { get;  set; }
    public Guid SmsBundleId { get;  set; }  
    public string? PaymentUrl { get; set; }
}