using GoTrack.Mobile.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests;
using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.Requests.AddVehiclesRequests.DTOs;

public class AddVehiclesRequestDto : FullAuditedEntityDto<Guid>
{
    public List<SubscriptionVehicleInfoDto> TrackVehicles { get; set; } = [];
    public string TrackerInstallationLocation { get; set; } = string.Empty;
    public Guid TrackAccountId { get; set; }
    public bool HasValidDevice { get; set; }
}
