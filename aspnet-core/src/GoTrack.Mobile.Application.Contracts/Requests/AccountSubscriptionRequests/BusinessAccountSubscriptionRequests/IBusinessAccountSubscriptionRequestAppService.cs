using System;
using System.Threading.Tasks;
using GoTrack.Mobile.Payments.Bills.DTOs;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public interface IBusinessAccountSubscriptionRequestAppService
{
    Task<BusinessAccountSubscriptionRequestDetailsDto> GetAsync(Guid id);
    Task<Guid> CreateAsync(BusinessAccountSubscriptionRequestCreateDto createDto);
    Task<BillDto> CreateTempBillAsync(BusinessAccountSubscriptionRequestCreateDto request);
    Task<BillDto> CreateTempBillWithoutVehiclesDataAsync(BusinessAccountSubscriptionRequestCreateWithoutVehiclesDataDto request);
}