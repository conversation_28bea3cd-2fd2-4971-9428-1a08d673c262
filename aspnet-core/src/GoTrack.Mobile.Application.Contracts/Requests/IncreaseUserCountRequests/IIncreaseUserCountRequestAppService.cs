using System;
using System.Threading.Tasks;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests;
using GoTrack.Mobile.Requests.IncreaseUserCountRequests.DTOs;

namespace GoTrack.Mobile.Requests.IncreaseUserCountRequests;

public interface IIncreaseUserCountRequestAppService
{
     Task<Guid> CreateAsync(CreateIncreaseUserCountRequestDto createIncreaseUserCountRequestDto);
     Task<IncreaseUserCountRequestDto> GetAsync(Guid id);
     Task<BillDto> CreateTempBillAsync(CreateIncreaseUserCountRequestDto request);
    Task<string> CreatePaymentAsync(CreateAccountRequestPaymentDto input);
}