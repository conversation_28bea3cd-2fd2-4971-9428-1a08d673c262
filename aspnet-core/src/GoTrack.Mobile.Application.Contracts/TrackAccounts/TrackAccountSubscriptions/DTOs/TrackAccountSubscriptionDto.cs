using System;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions.DTOs;

public class TrackAccountSubscriptionDto : FullAuditedEntityDto<Guid>
{
    public string SubscriptionPlanKey { get;  set; }
    public string SubscriptionPlanLocalizedName { get; set; } = string.Empty;
    public Guid TrackAccountId { get; set; }
    public int UserCount { get; set; }
    public int SmsBundleCount { get; set; }
    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public TrackAccountSubscriptionState State { get; set; }
    public DateTime? LastNotificationAt { get; set; }
}