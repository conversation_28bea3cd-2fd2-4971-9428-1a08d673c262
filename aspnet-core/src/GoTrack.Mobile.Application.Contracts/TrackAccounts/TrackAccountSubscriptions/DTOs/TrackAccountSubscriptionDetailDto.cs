using System;
using GoTrack.Mobile.Payments.Discounts.DTOs;
using GoTrack.Mobile.SmsBundles.DTOs;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions.DTOs;

public class TrackAccountSubscriptionDetailDto : FullAuditedEntityDto<Guid>
{
    public string SubscriptionPlanKey { get;  set; }
    public string SubscriptionPlanLocalizedName { get; set; } = string.Empty;
    public Guid TrackAccountId { get; set; }
    public int UserCount { get; set; }
    public int CurrentUserCount { get; set; }
    public int CurrentActiveUserCount { get; set; }

    public int SmsBundleCount { get; set; }
    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public TrackAccountSubscriptionState State { get; set; }
    public DateTime? LastNotificationAt { get; set; }
    public SmsBundleDto SmsBundle { get; set; }
    public string TrackerInstallationLocation { get; set; } = string.Empty;
    public int RemainingSubscriptionDurationInMonth { get; set; }
    public int SubscriptionDurationInMonth => (int)Math.Ceiling((To - From).TotalDays / 30);
}