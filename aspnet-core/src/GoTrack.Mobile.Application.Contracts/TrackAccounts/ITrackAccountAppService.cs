using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Mobile.TrackAccounts.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.FeatureManagement;

namespace GoTrack.Mobile.TrackAccounts;

public interface ITrackAccountAppService : IApplicationService
{
    Task<PagedResultDto<TrackAccountDto>> GetListAsync(PagedResultRequestDto requestDto);
    Task<List<FeatureDto>> GetFeaturesAsync(Guid id);
    Task<TrackAccountDetailsDto> GetAsync(Guid id);
}