using System;
using System.Threading.Tasks;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions;

public interface ITrackAccountSubscriptionAppService  : IApplicationService
{
    Task<TrackAccountSubscriptionDetailDto> GetAsync(Guid id);
    Task<PagedResultDto<TrackAccountSubscriptionDto>> GetListAsync(PagedResultRequestDto requestDto);
}