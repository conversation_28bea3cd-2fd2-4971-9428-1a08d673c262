namespace Warp10Abstraction.Sensors;

public static class SensorWarpClassMapper
{
    private static readonly Dictionary<Sensor, string> Mapping = new()
    {
        { Sensor.Ignition, "ignition" },
        { Sensor.Vibration , "number_of_vibration" },
        { Sensor.ExternalVoltage, "external_power_voltage"},
        { Sensor.Speed, "speed" },
        { Sensor.Lbs_Speed, "lbs_speed" },
        { Sensor.ServerTime, "server_time"}
    };

    public static string GetWarpClassName(Sensor sensor)
    {
        return Mapping[sensor];
    }

    public static string GetWarpClassNames(List<Sensor> sensors)
    {
        if (!sensors.Any())
            return "~.*";

        return "~" + string.Join("|", sensors.Select(GetWarpClassName));
    }

}