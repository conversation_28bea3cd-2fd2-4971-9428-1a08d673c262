#nullable enable
using Warp10.TimePeriods;

namespace Warp10.RunningTimes
{
    public interface IRunningTimeCalculator
    {
        Task<List<TimePeriod>> GetOnlyMovingAsync(string imei, DateTime from, DateTime to , RunningTimeCalculatorOptions calcOpt);
        Task<List<TimePeriod>> GetAllAsync(string imei, DateTime from, DateTime to , RunningTimeCalculatorOptions calcOpt); 
        Task<RunningTime> GetDetailedAsync(string imei, DateTime from, DateTime to ,RunningTimeCalculatorOptions calcOpt);


    }
}