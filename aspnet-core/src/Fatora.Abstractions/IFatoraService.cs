using Fatora.Abstractions.DTO.CreatePaymentDtos;
using Fatora.Abstractions.DTO.PaymentStatusDtos;
using Fatora.Abstractions.DTO.RevesalPaymentDto;

namespace Fatora.Abstractions;

public interface IFatoraService
{
    public Task<CreatePaymentResponseData> CreatePaymentAsync(CreatePaymentRequestDto createPaymentRequestDto, CancellationToken cancellationToken = default);

    public Task<GetPaymentStatusResponseData> GetPaymentStatus(Guid paymentId, CancellationToken cancellationToken = default);

    public Task ReversalPayment(ReversalPaymentRequestDto reversalPaymentRequestDto, CancellationToken cancellationToken = default);
}