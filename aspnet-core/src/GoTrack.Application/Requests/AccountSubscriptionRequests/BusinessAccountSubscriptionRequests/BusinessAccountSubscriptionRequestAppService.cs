using GoTrack.Identity;
using GoTrack.Payments;
using GoTrack.Permissions;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public class BusinessAccountSubscriptionRequestAppService : GoTrackAppService,
    IBusinessAccountSubscriptionRequestAppService
{
    private readonly IRepository<BusinessAccountSubscriptionRequest, Guid>
        _businessAccountSubscriptionRequestRepository;

    private readonly RequestManager _requestManager;
    private readonly IBusinessAccountSubscriptionRequestManager _subscriptionRequestManager;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;

    public BusinessAccountSubscriptionRequestAppService(
        IBusinessAccountSubscriptionRequestManager subscriptionRequestManager,
        IRepository<BusinessAccountSubscriptionRequest, Guid> businessAccountSubscriptionRequestRepository,
        RequestManager requestManager,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository)
    {
        _subscriptionRequestManager = subscriptionRequestManager;
        _businessAccountSubscriptionRequestRepository = businessAccountSubscriptionRequestRepository;
        _requestManager = requestManager;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
    }

    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestIndex)]
    public virtual async Task<PagedResultDto<BusinessAccountSubscriptionRequestDto>> GetListAsync(
        PagedResultRequestDto input)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();

        var query = await _businessAccountSubscriptionRequestRepository.WithDetailsAsync(request => request.Owner);

        var count = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var requests = await AsyncExecuter.ToListAsync(query);

        var requestDtos =
            ObjectMapper.Map<
                List<BusinessAccountSubscriptionRequest>,
                List<BusinessAccountSubscriptionRequestDto>
            >(requests);

        return new PagedResultDto<BusinessAccountSubscriptionRequestDto>(count, requestDtos);
    }

    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestDetails)]
    public virtual async Task<BusinessAccountSubscriptionRequestDetailsDto> GetAsync(Guid id)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();

        var query = await _businessAccountSubscriptionRequestRepository.WithDetailsAsync(request => request.Owner);

        query = query.Where(subscriptionRequest => subscriptionRequest.Id == id);

        var request = await AsyncExecuter.SingleOrDefaultAsync(query) ??
                      throw new EntityNotFoundException(typeof(BusinessAccountSubscriptionRequest), id);

        var userFatoraPaymentQuery = await _userFatoraPaymentRepository.GetQueryableAsync();

        userFatoraPaymentQuery = userFatoraPaymentQuery
            .Where(x => x.Id == id && x.PaymentStatus == PaymentStatus.Pending)
            .OrderByDescending(x => x.CreationTime);

        var userFatoraPayment = await AsyncExecuter.FirstOrDefaultAsync(userFatoraPaymentQuery);

        var businessAccountSubscriptionRequestDetailsDto =
            ObjectMapper.Map<BusinessAccountSubscriptionRequest, BusinessAccountSubscriptionRequestDetailsDto>(request);

        businessAccountSubscriptionRequestDetailsDto.PaymentUrl = userFatoraPayment?.Url;

        return businessAccountSubscriptionRequestDetailsDto;
    }

    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestStartProcessing)]
    public virtual async Task StartProcessingAsync(Guid id, StartProcessingDto createDto)
    {
        await _subscriptionRequestManager.ApplyDiscountAsync(id, null, createDto.Note);
    }

    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestInstallDevices)]
    public virtual async Task InstallDevicesAsync(Guid id, InstallDevicesDto installDevicesDto)
    {
        var duplicateDeviceIds = installDevicesDto.VehicleDeviceDtos
            .GroupBy(v => v.DeviceId)
            .Where(g => g.Count() > 1)
            .ToList();

        if (duplicateDeviceIds.Count is not 0)
        {
            throw new UserFriendlyException(L[GoTrackAdminApplicationErrorCodes.DuplicateDevice]);
        }

        var deviceRequests = installDevicesDto.VehicleDeviceDtos.Select(x => new DeviceInstallationRequest()
        {
            DeviceId = x.DeviceId,
            LicensePlateSubClass = x.LicensePlate.SubClass,
            Serial = x.LicensePlate.Serial
        }).ToList();
        
        await _subscriptionRequestManager.InstallDevicesAsync(id,deviceRequests);
    }

    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestFinishProcessing)]
    public virtual async Task FinishProcessingAsync(Guid id, FinishProcessingDto finishProcessingDto)
    {
        await _subscriptionRequestManager.FinishProcessingAsync(id, finishProcessingDto.Note);
    }


    [Authorize(GoTrackPermissions.BusinessAccountSubscriptionRequestReject)]
    public virtual async Task RejectAsync(Guid id, RejectReqDto dto)
    {
        var request = await _businessAccountSubscriptionRequestRepository.GetAsync(id);
        await _requestManager.RejectAsync(id, dto.RejectReason);
        await _businessAccountSubscriptionRequestRepository.UpdateAsync(request);
    }
}