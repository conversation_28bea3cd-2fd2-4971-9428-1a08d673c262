using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Permissions;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.DTOs;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions;

public class TrackAccountSubscriptionAppService : GoTrackAppService, ITrackAccountSubscriptionAppService
{
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    public TrackAccountSubscriptionAppService(
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository)
    {
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
    }

    [Authorize(GoTrackPermissions.TrackAccountSubscriptionDetails)]
    public async Task<TrackAccountSubscriptionDetailDto> GetAsync(Guid id)
    {
        var subscription = await _trackAccountSubscriptionRepository.GetAsync(id);
        return ObjectMapper.Map<TrackAccountSubscription, TrackAccountSubscriptionDetailDto>(subscription);
    }

    [Authorize(GoTrackPermissions.TrackAccountSubscriptionIndex)]
    public async Task<PagedResultDto<TrackAccountSubscriptionDto>> GetListAsync(
        PagedResultRequestDto requestDto)
    {
        var query = await _trackAccountSubscriptionRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(requestDto);

        var trackAccounts = await AsyncExecuter.ToListAsync(query);

        var trackAccountDtos =
            ObjectMapper.Map<List<TrackAccountSubscription>, List<TrackAccountSubscriptionDto>>(trackAccounts);

        return new PagedResultDto<TrackAccountSubscriptionDto>(totalCount, trackAccountDtos);
    }


}