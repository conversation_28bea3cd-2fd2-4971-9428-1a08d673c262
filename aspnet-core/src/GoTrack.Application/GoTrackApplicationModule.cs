using GoTrack.Identity;
using GoTrack.TrackAccounts;
using MailKit.Security;
using Volo.Abp.Account;
using Volo.Abp.AutoMapper;
using Volo.Abp.Data;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;
using Volo.Abp.FluentValidation;
using Volo.Abp.MailKit;
using Volo.Abp.Features;


namespace GoTrack;

[DependsOn(
    typeof(GoTrackDomainModule),
    typeof(AbpAccountApplicationModule),
    typeof(GoTrackApplicationContractsModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule)
    )]
[DependsOn(typeof(AbpFluentValidationModule))]
    public class GoTrackApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
   
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<GoTrackApplicationModule>();
        });
        Configure<AbpMailKitOptions>(options =>
        {
            options.SecureSocketOption = SecureSocketOptions.SslOnConnect;
        });



        //Important: this essentially ensures only HostTenantUsers are being displayed
        Configure<AbpDataFilterOptions>(options =>
        {
            options.DefaultStates[typeof(IHostTenantUserFilter)] = new DataFilterState(isEnabled: true);
            options.DefaultStates[typeof(ICustomerUserFilter)] = new DataFilterState(isEnabled: false);
            options.DefaultStates[typeof(IHaveTrackAccount)] = new DataFilterState(isEnabled: false);
        });
    }
}
