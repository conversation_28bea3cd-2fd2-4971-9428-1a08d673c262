using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.SmsBundles.DTOs;
using GoTrack.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.SmsBundles;

[Authorize]
public class SmsBundleAppService : GoTrackAppService, ISmsBundleAppService
{
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepository;

    public SmsBundleAppService(IRepository<SmsBundle, Guid> smsBundleRepository)
    {
        _smsBundleRepository = smsBundleRepository;
    }

    [Authorize(GoTrackPermissions.SmsBundleCreate)]
    public virtual async Task<SmsBundleDto> CreateAsync(SmsBundleCreateDto input)
    {
        var smsBundle = new SmsBundle(
            Guid.NewGuid(),
            input.Name,
            input.MessagesCount,
            input.Price
        );

        await _smsBundleRepository.InsertAsync(smsBundle);

        return ObjectMapper.Map<SmsBundle, SmsBundleDto>(smsBundle);
    }

    [Authorize(GoTrackPermissions.SmsBundleDetails)]
    public virtual async Task<SmsBundleDto> GetAsync(Guid id)
    {
        var smsBundle = await _smsBundleRepository.GetAsync(id);

        return ObjectMapper.Map<SmsBundle, SmsBundleDto>(smsBundle);
    }

    [Authorize(GoTrackPermissions.SmsBundleIndex)]
    public virtual async Task<PagedResultDto<SmsBundleDto>> GetListAsync(PagedResultRequestDto input)
    {
        var query = await _smsBundleRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var smsBundles = await AsyncExecuter.ToListAsync(query);

        var smsBundlesDtos = ObjectMapper.Map<List<SmsBundle>, List<SmsBundleDto>>(smsBundles);

        return new PagedResultDto<SmsBundleDto>(totalCount, smsBundlesDtos);
    }

    [Authorize(GoTrackPermissions.SmsBundleUpdate)]
    public virtual async Task<SmsBundleDto> UpdateAsync(Guid id, SmsBundleCreateDto input)
    {
        var smsBundle = await _smsBundleRepository.GetAsync(id);

        smsBundle.SetMessagesCount(input.MessagesCount);
        smsBundle.SetPrice(input.Price);
        smsBundle.SetName(input.Name);

        await _smsBundleRepository.UpdateAsync(smsBundle);

        return ObjectMapper.Map<SmsBundle, SmsBundleDto>(smsBundle);
    }

    [Authorize(GoTrackPermissions.SmsBundleDelete)]
    public virtual async Task DeleteAsync(Guid id)
    {
        await _smsBundleRepository.DeleteAsync(id);
    }
}