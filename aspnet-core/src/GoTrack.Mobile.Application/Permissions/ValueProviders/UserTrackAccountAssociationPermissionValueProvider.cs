using System.Linq;
using System.Threading.Tasks;
using GoTrack.TrackAccounts;
using Volo.Abp;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Security.Claims;

namespace GoTrack.Mobile.Permissions.ValueProviders;

public class UserTrackAccountAssociationPermissionValueProvider : PermissionValueProvider
{
    public const string ProviderName = "UserTrackAccountAssociation";
    public override string Name => ProviderName;

    private readonly ICurrentTrackAccount _currentTrackAccount;

    public UserTrackAccountAssociationPermissionValueProvider(IPermissionStore permissionStore,
        ICurrentTrackAccount currentTrackAccount)
        : base(permissionStore)
    {
        _currentTrackAccount = currentTrackAccount;
    }

    public override async Task<PermissionGrantResult> CheckAsync(PermissionValueCheckContext context)
    {
        var userId = context.Principal?.FindFirst(AbpClaimTypes.UserId)?.Value;
        var trackAccountId = _currentTrackAccount.Id;

        if (userId is null || trackAccountId is null)
        {
            return PermissionGrantResult.Undefined;
        }

        return await PermissionStore.IsGrantedAsync(context.Permission.Name, Name, trackAccountId + userId)
            ? PermissionGrantResult.Granted
            : PermissionGrantResult.Undefined;
    }

    public override async Task<MultiplePermissionGrantResult> CheckAsync(PermissionValuesCheckContext context)
    {
        var permissionNames = context.Permissions.Select(x => x.Name).Distinct().ToArray();
        Check.NotNullOrEmpty(permissionNames, nameof(permissionNames));

        var userId = context.Principal?.FindFirst(AbpClaimTypes.UserId)?.Value;
        var trackAccountId = _currentTrackAccount.Id;

        if (userId is null || trackAccountId is null)
        {
            return new MultiplePermissionGrantResult(permissionNames);
        }

        return await PermissionStore.IsGrantedAsync(permissionNames, Name, trackAccountId + userId);
    }
}