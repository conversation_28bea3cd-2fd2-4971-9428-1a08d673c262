using System.Collections.Generic;
using System.Drawing;
using System;
using Volo.Abp.Validation;
using System.ComponentModel.DataAnnotations;

namespace GoTrack.Mobile;

public class ColorHelper
{
    public static Color TryGetColor(string color)
    {
        try
        {
            return ColorTranslator.FromHtml(color);
        }
        catch (Exception)
        {
            throw new AbpValidationException("", new List<ValidationResult>()
            {
                new("Invalid Color")
            });
        }
    }
}
