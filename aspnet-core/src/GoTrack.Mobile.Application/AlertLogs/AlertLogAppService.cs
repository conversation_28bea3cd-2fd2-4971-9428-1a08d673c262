using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Alerts.AlertLogs;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers;
using GoTrack.Mobile.AlertLogs.DTOs;
using GoTrack.Notifications.NotificationFactories.AlertNotifications;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;

namespace GoTrack.Mobile.AlertLogs;

// [RequiresFeature([
//     GoTrackFeatureDefinitions.ExceedingSpeedAlert,
//     GoTrackFeatureDefinitions.JobTimeAlert,
//     GoTrackFeatureDefinitions.ExitingRouteAlert,
//     GoTrackFeatureDefinitions.EnteringZoneAlert,
//     GoTrackFeatureDefinitions.ExitingZoneAlert,
// ],RequiresAll = false)]
public class AlertLogAppService : GoTrackMobileAppService, IAlertLogAppService
{
    private readonly IRepository<AlertLog, Guid> _alertLogsRepository;
    private readonly AlertNotificationManager _alertNotificationManager;
    private readonly IRepository<AlertTrigger, Guid> _alertTriggerRepository;

    public AlertLogAppService(IRepository<AlertLog, Guid> alertLogsRepository, AlertNotificationManager alertNotificationManager, IRepository<AlertTrigger, Guid> alertTriggerRepository)
    {
        _alertLogsRepository = alertLogsRepository;
        _alertNotificationManager = alertNotificationManager;
        _alertTriggerRepository = alertTriggerRepository;
    }

    public async Task SendTest(Guid alertTriggerId)
    {
        var alertTrigger = await _alertTriggerRepository.GetAsync(alertTriggerId);
        await _alertNotificationManager.SendNotificationAsync(alertTrigger, Clock.Now);
    }
    

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<AlertLogDto>> GetListAsync(AlertLogPagedResultRequestDto input, DateTime? fromDate,
        DateTime? toDate)
    {
        var query = await _alertLogsRepository.WithDetailsAsync(log => log.Device,
            log => log.Vehicle, log => log.AlertTrigger);
        query = query.OrderBy(log => log.EndedAt).ThenBy(log => log.StartedAt);
        if (input.DeviceId is not null)
        {
            query = query.Where(log => log.Device.Id == input.DeviceId);
        }

        if (input.VehicleId is not null)
        {
            query = query.Where(log => log.Vehicle.Id == input.VehicleId);
        }

        if (fromDate.HasValue || toDate.HasValue)
        {
            query = query.Where(log =>
                (!fromDate.HasValue || log.StartedAt >= fromDate.Value || log.EndedAt >= fromDate.Value) &&
                (!toDate.HasValue || log.StartedAt <= toDate.Value || log.EndedAt <= toDate.Value)
            );
        }

        query = query.PageBy(input);
        var count = await AsyncExecuter.CountAsync(query);

        var alertLogs = await AsyncExecuter.ToListAsync(query);
        return new PagedResultDto<AlertLogDto>(count,
            ObjectMapper.Map<List<AlertLog>, List<AlertLogDto>>
                (alertLogs));
    }
}