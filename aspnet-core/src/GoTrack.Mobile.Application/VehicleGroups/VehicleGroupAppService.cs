using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Observations;
using GoTrack.TrackAccounts;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleGroups;
using GoTrack.Vehicles;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;


namespace GoTrack.Mobile.VehicleGroups;

[RequiresFeature(GoTrackFeatureDefinitions.GroupManagement)]
public class VehicleGroupAppService : GoTrackMobileAppService, IVehicleGroupAppService
{
    private readonly IRepository<TrackAccount, Guid> _trackAccountRepository;
    private readonly IRepository<Vehicle, Guid> _vehicleRepository;
    private readonly ICurrentTrackAccount _currentTrackAccount;
    private readonly IRepository<VehicleGroup, Guid> _vehicleGroupRepository;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IRepository<ObservationVehicleGroup, Guid> _observationVehicleGroupRepository;
    private readonly IRepository<VehicleGroupVehicle, Guid> _vehicleGroupVehicleRepository;

    public VehicleGroupAppService(
        IRepository<TrackAccount, Guid> trackAccountRepository,
        IRepository<Vehicle, Guid> vehicleRepository, ICurrentTrackAccount currentTrackAccount,
        IRepository<VehicleGroup, Guid> vehicleGroupRepository,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IRepository<ObservationVehicleGroup, Guid> observationVehicleGroupRepository,
        IRepository<VehicleGroupVehicle, Guid> vehicleGroupVehicleRepository)
    {
        _trackAccountRepository = trackAccountRepository;
        _vehicleRepository = vehicleRepository;
        _currentTrackAccount = currentTrackAccount;
        _vehicleGroupRepository = vehicleGroupRepository;
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _observationVehicleGroupRepository = observationVehicleGroupRepository;
        _vehicleGroupVehicleRepository = vehicleGroupVehicleRepository;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<Guid> CreateAsync(VehicleGroupCreateDto createCreateDto)
    {
        if (await _vehicleGroupRepository.AnyAsync(x => x.Name == createCreateDto.Name))
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.DuplicateVehicleGroupName]);

        var trackAccount = await _trackAccountRepository.GetAsync(_currentTrackAccount.GetId());
        var vehicleGroup = new VehicleGroup(GuidGenerator.Create(), createCreateDto.Name, trackAccount.Id);

        foreach (var vehicleDto in createCreateDto.VehicleGroupVehicleCreateDtos)
        {
            var vehicle = await _vehicleRepository.GetAsync(vehicleDto.VehicleId);

            var newVehicleGroupVehicle = new VehicleGroupVehicle(GuidGenerator.Create(), vehicle.Id, vehicleGroup.Id);

            await _vehicleGroupVehicleRepository.InsertAsync(newVehicleGroupVehicle);

            await _vehicleRepository.UpdateAsync(vehicle);
        }

        await _vehicleGroupRepository.InsertAsync(vehicleGroup);
        return vehicleGroup.Id;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<VehicleGroupDetailsDto> GetAsync(Guid id)
    {
        var vehicleGroup = await _vehicleGroupRepository.GetAsync(id);

        return ObjectMapper.Map<VehicleGroup, VehicleGroupDetailsDto>(vehicleGroup);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<PagedResultDto<VehicleGroupDto>> GetListAsync(PagedResultRequestDto input)
    {
        var trackAccountId = _currentTrackAccount.GetId();

        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository
            .GetAsync(x =>
                x.TrackAccountId == trackAccountId
                && x.UserId == CurrentUser.Id
            );

        if (userTrackAccountAssociation.AssociationType is AssociationType.Observer)
            return await GetObservationVehicleGroupListAsync(input, userTrackAccountAssociation);

        var query = await _vehicleGroupRepository.GetQueryableAsync();

        var count = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        query = query.OrderByDescending(x => x.CreationTime);

        List<VehicleGroup> vehicleGroups = await AsyncExecuter.ToListAsync(query);

        return new PagedResultDto<VehicleGroupDto>(count, ObjectMapper.Map<List<VehicleGroup>, List<VehicleGroupDto>>(vehicleGroups));
    }

    private async Task<PagedResultDto<VehicleGroupDto>> GetObservationVehicleGroupListAsync(PagedResultRequestDto input, UserTrackAccountAssociation userTrackAccountAssociation)
    {
        var query = await _observationVehicleGroupRepository.WithDetailsAsync(x => x.VehicleGroup);

        query = query.Where(observer =>
            observer.UserTrackAccountAssociationId == userTrackAccountAssociation.Id
        );

        var hasAccess = await AsyncExecuter.AnyAsync(query);
        if (!hasAccess)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.NoAccess]);

        var count = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        query = query.OrderByDescending(x => x.CreationTime);

        var observationVehicleGroups = await AsyncExecuter.ToListAsync(query);

        var vehicleGroupsDto = ObjectMapper.Map<List<VehicleGroup>, List<VehicleGroupDto>>(observationVehicleGroups.Select(x => x.VehicleGroup).ToList());

        return new PagedResultDto<VehicleGroupDto>(count, vehicleGroupsDto);
    }
}