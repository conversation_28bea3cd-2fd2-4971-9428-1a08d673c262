using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.Routes.DTOs;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GoTrack.Mobile.Routes.Validators;

public class CreateRouteDtoValidator : AbstractValidator<CreateRouteDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public CreateRouteDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Name.Trim())
            .NotEmpty()
            .WithName(_localizer["GoTrack:Name"]);

        RuleFor(x => x.Line)
            .Must(x => !x.IsNullOrEmpty() 
                    && x.DistinctBy(point => new { point.LongitudeX, point.LatitudeY }).Count() > 1
            )
            .WithName(_localizer["GoTrack:Line"]);

        RuleFor(x => x.StopPoints)
            .NotNull()
            .WithName(_localizer["GoTrack:StopPoints"]);

        RuleFor(x => x.HexColor)
            .Must(x => !x.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:Color"]);
    }
}
