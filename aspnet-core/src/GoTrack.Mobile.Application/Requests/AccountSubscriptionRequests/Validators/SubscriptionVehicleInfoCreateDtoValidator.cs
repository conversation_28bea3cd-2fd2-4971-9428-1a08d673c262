using FluentValidation;
using GoTrack.Localization;
using Microsoft.Extensions.Localization;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.Validators;

public class SubscriptionVehicleInfoCreateDtoValidator : AbstractValidator<SubscriptionVehicleInfoCreateDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public SubscriptionVehicleInfoCreateDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.LicensePlateSerial.Trim())
            .NotEmpty()
            .WithName(_localizer["VehicleInfo:LicensePlateSerial"]);

        RuleFor(x => x.LicensePlateSubClass)
            .NotNull()
            .WithName(_localizer["VehicleInfo:LicensePlateSubClass"]);

        RuleFor(x => x.ConsumptionRate)
            .NotEmpty()
            .GreaterThan(0)
            .WithName(_localizer["GoTrack:ConsumptionRate"]);

        RuleFor(x => x.Color)
            .NotEmpty()
            .WithName(_localizer["GoTrack:Color"]);
    }
}
