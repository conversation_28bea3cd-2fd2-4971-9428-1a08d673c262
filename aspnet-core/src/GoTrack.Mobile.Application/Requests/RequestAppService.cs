using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;
using Volo.Abp.Users;

namespace GoTrack.Mobile.Requests;

public class RequestAppService : GoTrackMobileAppService, IRequestAppService
{
    private readonly IRepository<Request, Guid> _requestRepo;
    private readonly RequestManager _requestManager;

    protected IRepository<TrackAccountRequest, Guid> TrackAccountRequestRepository =>
        LazyServiceProvider.GetRequiredService<IRepository<TrackAccountRequest, Guid>>();

    public RequestAppService(IRepository<Request, Guid> requestRepo, RequestManager requestManager)
    {
        _requestRepo = requestRepo;
        _requestManager = requestManager;
    }

    [Authorize]
    public async Task<PagedResultDto<RequestDto>> GetListAsync(GetListRequestsInputDto inputDto)
    {
        var query = await _requestRepo.GetQueryableAsync();

        query = query.Where(request => request.OwnerId == CurrentUser.GetId());

        if (!inputDto.Status.IsNullOrEmpty())
        {
            var status = inputDto.Status.Select(EnumHelper.GetEnumValueByName<RequestStatus>).ToList();

            query = query.Where(x => status.Contains(x.Status));
        }

        if (!inputDto.Types.IsNullOrEmpty())
        {
            var types = inputDto.Types.Select(EnumHelper.GetEnumValueByName<RequestType>).ToList();

            query = query.Where(x => types.Contains(x.Type));
        }

        var totalCount = await AsyncExecuter.CountAsync(query);
        var requests = await AsyncExecuter.ToListAsync(query.PageBy(inputDto));

        var possListDto = ObjectMapper.Map<List<Request>, List<RequestDto>>(requests);

        return new PagedResultDto<RequestDto>(totalCount, possListDto);
    }

    [Authorize]
    public async Task CancelAsync(Guid id)
    {
        var request = await _requestRepo.GetAsync(id);
        await _requestManager.CancelAsync(id);
        await _requestRepo.UpdateAsync(request);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<RequestDto>> GetListOfTrackAccountId(GetRequestsOfTrackAccountInputDto input)
    {
        var query = await TrackAccountRequestRepository.GetQueryableAsync();

        if (!input.Status.IsNullOrEmpty())
        {
            var status = input.Status.Select(EnumHelper.GetEnumValueByName<RequestStatus>).ToList();

            query = query.Where(x => status.Contains(x.Status));
        }

        if (!input.Types.IsNullOrEmpty())
        {
            var types = input.Types.Select(EnumHelper.GetEnumValueByName<RequestType>).ToList();

            query = query.Where(x => types.Contains(x.Type));
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var requests = await AsyncExecuter.ToListAsync(query);

        var requestDtos = ObjectMapper.Map<List<TrackAccountRequest>, List<RequestDto>>(requests);

        return new PagedResultDto<RequestDto>(totalCount, requestDtos);
    }
}