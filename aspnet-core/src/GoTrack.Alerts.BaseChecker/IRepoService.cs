using System;
using System.Threading.Tasks;
using GoTrack.Alerts.BaseChecker.Models;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;

namespace GoTrack.Alerts.BaseChecker;

public interface IRepoService
{
    public GroupedAlertListBase[] GetGroupedAlertsList(int pageNumber, int pageSize);

    public int GetGroupedAlertsListCount();

    public AlertBase[] GetAlertsByIds(string[] ids);

    public AlertBase GetAlert(Guid evId);

    public void SaveUpdatedAlert();

    public Task ParseAndSaveAlert(AlertCrudToService alert);

    public Task DeleteAlert(AlertBase alertBase);
}