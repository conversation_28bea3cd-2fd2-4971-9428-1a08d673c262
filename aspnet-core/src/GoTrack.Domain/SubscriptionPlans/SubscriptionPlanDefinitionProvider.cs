using System;
using GoTrack.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Localization;

namespace GoTrack.SubscriptionPlans;

public class SubscriptionPlanDefinitionProvider : ITransientDependency
{
    public void Define(ISubscriptionPlanDefinitionContext context)
    {
        context.Add(
            new SubscriptionPlanDefinition(
                SubscriptionPlanKeys.Silver,
                L(SubscriptionPlanKeys.Silver),
                null,
                1,
                [
                    GoTrackFeatureDefinitions.LiveMonitoring,
                    GoTrackFeatureDefinitions.ShowVehiclePath,
                    GoTrackFeatureDefinitions.ExceedingSpeedAlert,
                    GoTrackFeatureDefinitions.ObserverManagement,
                    GoTrackFeatureDefinitions.SendTextMessageOnEveryAlarm,
                    GoTrackFeatureDefinitions.Dashboard,
                    GoTrackFeatureDefinitions.TechnicalSupport,
                    GoTrackFeatureDefinitions.Notifications,
                    GoTrackFeatureDefinitions.ShareTheSite,
                ],
                true,
                new DateTime(2020, 1, 1)
            )
        );

        context.Add(
            new SubscriptionPlanDefinition(
                SubscriptionPlanKeys.Gold,
                L(SubscriptionPlanKeys.Gold),
                null,
                2,
                [
                    GoTrackFeatureDefinitions.LiveMonitoring,
                    GoTrackFeatureDefinitions.ShowVehiclePath,
                    GoTrackFeatureDefinitions.ExceedingSpeedAlert,
                    GoTrackFeatureDefinitions.ExitingRouteAlert,
                    GoTrackFeatureDefinitions.EnteringZoneAlert,
                    GoTrackFeatureDefinitions.ExitingZoneAlert,
                    GoTrackFeatureDefinitions.Reports,
                    GoTrackFeatureDefinitions.GeographicAreaManagement,
                    GoTrackFeatureDefinitions.PathManagement,
                    GoTrackFeatureDefinitions.StopPointManagement,
                    GoTrackFeatureDefinitions.ObserverManagement,
                    GoTrackFeatureDefinitions.SendTextMessageOnEveryAlarm,
                    GoTrackFeatureDefinitions.Dashboard,
                    GoTrackFeatureDefinitions.TechnicalSupport,
                    GoTrackFeatureDefinitions.Notifications,
                    GoTrackFeatureDefinitions.ShareTheSite,
                ],
                true,
                new DateTime(2020, 1, 1)
            )
        );

        context.Add(
            new SubscriptionPlanDefinition(
                SubscriptionPlanKeys.Platinum,
                L(SubscriptionPlanKeys.Platinum),
                null,
                3,
                [
                    GoTrackFeatureDefinitions.LiveMonitoring,
                    GoTrackFeatureDefinitions.ShowVehiclePath,
                    GoTrackFeatureDefinitions.ExceedingSpeedAlert,
                    GoTrackFeatureDefinitions.JobTimeAlert,
                    GoTrackFeatureDefinitions.ExitingRouteAlert,
                    GoTrackFeatureDefinitions.EnteringZoneAlert,
                    GoTrackFeatureDefinitions.ExitingZoneAlert,
                    GoTrackFeatureDefinitions.Reports,
                    GoTrackFeatureDefinitions.GeographicAreaManagement,
                    GoTrackFeatureDefinitions.PathManagement,
                    GoTrackFeatureDefinitions.StopPointManagement,
                    GoTrackFeatureDefinitions.GroupManagement,
                    GoTrackFeatureDefinitions.ObserverManagement,
                    GoTrackFeatureDefinitions.SendTextMessageOnEveryAlarm,
                    GoTrackFeatureDefinitions.Dashboard,
                    GoTrackFeatureDefinitions.TechnicalSupport,
                    GoTrackFeatureDefinitions.Notifications,
                    GoTrackFeatureDefinitions.ShareTheSite,
                ],
                true,
                new DateTime(2020, 1, 1)
            )
        );

        context.Add(
            new SubscriptionPlanDefinition(
                SubscriptionPlanKeys.PlatinumTrial,
                L(SubscriptionPlanKeys.PlatinumTrial),
                [1],
                3,
                [
                    GoTrackFeatureDefinitions.LiveMonitoring,
                    GoTrackFeatureDefinitions.ShowVehiclePath,
                    GoTrackFeatureDefinitions.ExceedingSpeedAlert,
                    GoTrackFeatureDefinitions.JobTimeAlert,
                    GoTrackFeatureDefinitions.ExitingRouteAlert,
                    GoTrackFeatureDefinitions.EnteringZoneAlert,
                    GoTrackFeatureDefinitions.ExitingZoneAlert,
                    GoTrackFeatureDefinitions.Reports,
                    GoTrackFeatureDefinitions.GeographicAreaManagement,
                    GoTrackFeatureDefinitions.PathManagement,
                    GoTrackFeatureDefinitions.StopPointManagement,
                    GoTrackFeatureDefinitions.GroupManagement,
                    GoTrackFeatureDefinitions.ObserverManagement,
                    GoTrackFeatureDefinitions.SendTextMessageOnEveryAlarm,
                    GoTrackFeatureDefinitions.Dashboard,
                    GoTrackFeatureDefinitions.TechnicalSupport,
                    GoTrackFeatureDefinitions.Notifications,
                    GoTrackFeatureDefinitions.ShareTheSite,
                ],
                true,
                new DateTime(2020, 1, 1)
            )
        );
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<GoTrackResource>(name);
    }
}