using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using GoTrack.GeoZones;
using GoTrack.PolyLines;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Warp10Abstraction.Models;

namespace GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers;

public class ExitingZoneAlertTriggerManager : AlertTriggerManager
{
    private readonly IRepository<ExitingZoneAlertTrigger, Guid> _exitingZoneAlertTriggerRepository;
    private readonly IRepository<GeoZone, Guid> _geoZoneRepository;

    public ExitingZoneAlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory,
        IRepository<ExitingZoneAlertTrigger, Guid> exitingZoneAlertTriggerRepository,
        IRepository<GeoZone, Guid> geoZoneRepository)
        : base(
            alertTriggerRepository,
            alertDefinitionAssociationRepository,
            vehicleDeviceEventLogRepository,
            alertDefinitionRepository,
            publishEndpoint,
            stringLocalizerFactory
        )
    {
        _exitingZoneAlertTriggerRepository = exitingZoneAlertTriggerRepository;
        _geoZoneRepository = geoZoneRepository;
    }

    public override async Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime)
    {
        var query = await _exitingZoneAlertTriggerRepository.WithDetailsAsync(x => x.Vehicle, x => x.GeoZone);

        var exitingZoneAlertTrigger = await AsyncExecuter.SingleAsync(query.Where(x => x.Id == alertTriggerId));
        
        var message = Localizer[
            "GoTrack:ExitingZoneMessage",
            exitingZoneAlertTrigger.Vehicle.LicensePlate.Serial,
            exitingZoneAlertTrigger.GeoZone.Name,
            notificationDateTime
        ];

        return message;
    }


    public override List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles)
    {
        var exitingZoneAlertDefinition = alertDefinition as ExitingZoneAlertDefinition;

        return uniqueVehicles
            .SelectMany(vehicle => exitingZoneAlertDefinition!.ZoneAlertGeoZones
                .Select(zoneAlertGeoZone => new ExitingZoneAlertTrigger(
                    GuidGenerator.Create(),
                    [.. exitingZoneAlertDefinition.NotificationMethods],
                    exitingZoneAlertDefinition.TrackAccountId,
                    exitingZoneAlertDefinition.Id,
                    vehicle.Id,
                    zoneAlertGeoZone.GeoZoneId
                ))
            ).ToList<AlertTrigger>();
    }

    public override async Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType)
    {
        var exitingZoneAlertTrigger = trigger as ExitingZoneAlertTrigger;

        var lastVehicleDeviceEventLog = (await VehicleDeviceEventLogRepository.WithDetailsAsync(x => x.Device))
                .OrderByDescending(e => e.CreationTime)
                .FirstOrDefault(log => log.VehicleId == exitingZoneAlertTrigger!.VehicleId && log.EventName == EventName.Installed);

        var exitingZoneDefinitionZone = await _geoZoneRepository.GetAsync(exitingZoneAlertTrigger!.GeoZoneId);
        var exitingZoneHhCode = await WarpLib.GetZoneHhCodeAsync(
            PolyLine.Decode(exitingZoneDefinitionZone.Polyline.Line).Select(coordinate =>
                new WarpCoordinate(coordinate.Longitude, coordinate.Latitude)
            )
            .ToList()
        );

        return new ZoneInOutAlertCrud()
        {
            Id = exitingZoneAlertTrigger.Id,
            Code = AlertCode.ZoneOut,
            AffectiveFrom = Clock.Now,
            CrudType = crudType,
            Imei = lastVehicleDeviceEventLog?.Device.Imei,
            Polygon = exitingZoneHhCode
        };
    }
}
