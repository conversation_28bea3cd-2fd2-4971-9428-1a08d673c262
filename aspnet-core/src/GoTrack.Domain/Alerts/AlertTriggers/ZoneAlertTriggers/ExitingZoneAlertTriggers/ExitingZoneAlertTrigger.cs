using GoTrack.AlertDefinitions;
using GoTrack.GeoZones;
using System;
using System.Collections.Generic;

namespace GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers;

public class ExitingZoneAlertTrigger : AlertTrigger
{
    public Guid GeoZoneId { get; private set; }

    #region Navigation

    public GeoZone GeoZone { get; private set; }

    #endregion

    private ExitingZoneAlertTrigger() { }

    public ExitingZoneAlertTrigger(
        Guid id,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId,
        Guid alertDefinitionId,
        Guid vehicleId,
        Guid geoZoneId
        ) : base(
            id,
            AlertType.ExitingZone,
            notificationMethods,
            trackAccountId,
            alertDefinitionId,
            vehicleId
        )
    {
        GeoZoneId = geoZoneId;
    }

}
