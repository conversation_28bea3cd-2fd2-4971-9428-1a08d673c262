using GoTrack.AlertDefinitions;
using GoTrack.GeoZones;
using System;
using System.Collections.Generic;

namespace GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.EnteringZoneAlertTriggers;

public class EnteringZoneAlertTrigger : AlertTrigger
{
    public Guid GeoZoneId { get; private set; }

    #region Navigation

    public GeoZone GeoZone { get; private set; }

    #endregion
    private EnteringZoneAlertTrigger() { }

    public EnteringZoneAlertTrigger(
    Guid id,
    List<AlertDefinitionNotificationMethod> notificationMethods,
    Guid trackAccountId,
    Guid alertDefinitionId,
    Guid vehicleId,
    Guid geoZoneId
    ) : base(
            id,
            AlertType.EnteringZone,
            notificationMethods,
            trackAccountId,
            alertDefinitionId,
            vehicleId
        )
    {
        GeoZoneId = geoZoneId;
    }
}
