using System;
using System.Threading.Tasks;
using GoTrack.AlertDefinitions;
using Volo.Abp.Domain.Services;

namespace GoTrack.Alerts.AlertLogs;

public interface IAlertLogManager : IDomainService
{
    Task<AlertLog> CreateAsync(Guid id, Guid vehicleId, Guid deviceId, AlertType alertType, Guid alertTriggerId, Guid trackAccountId, DateTime from, DateTime to);
    Task<(bool isNewAlert, AlertLog alertLog)> HandleRaisedAlertConsumerAlertLogAsync(Guid alertTriggerId, DateTime raisedStart, DateTime raisedEnd);
}