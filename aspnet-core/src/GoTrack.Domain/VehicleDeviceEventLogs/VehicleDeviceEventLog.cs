using System;
using GoTrack.Devices;
using GoTrack.Vehicles;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.VehicleDeviceEventLogs;

public class VehicleDeviceEventLog : FullAuditedAggregateRoot<Guid>
{

    public Guid VehicleId { get; private set; }
    public Guid DeviceId { get; private set; }
    public EventName EventName { get; private set; }

    //navigation prop
    public Device Device { get; private set; }
    public Vehicle Vehicle { get; private set; }

    private VehicleDeviceEventLog()
    {
        
    }
    internal VehicleDeviceEventLog(Guid vehicleId, Guid deviceId, EventName eventName)
    {
        VehicleId = vehicleId;
        DeviceId = deviceId;
        EventName = eventName;
    }

}