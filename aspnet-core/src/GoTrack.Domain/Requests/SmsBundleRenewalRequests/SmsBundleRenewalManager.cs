using System;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.SmsBundles;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests.SmsBundleRenewalRequests;

public class SmsBundleRenewalManager : DomainService, ISmsBundleRenewalManager
{
    private readonly IRepository<SmsBundleRenewalRequest, Guid> _renewalRequestRepository;
    private readonly IRepository<TrackAccountSubscription, Guid> _subscriptionRepository;
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepository;
    private readonly ITrackAccountRepository _trackAccountRepository;
    protected BillManager BillManager => LazyServiceProvider.GetRequiredService<BillManager>();
    protected SmsBundleRenewalRequestBillPlanFactory SmsBundleRenewalRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<SmsBundleRenewalRequestBillPlanFactory>();
    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();


    public SmsBundleRenewalManager(
        IRepository<SmsBundleRenewalRequest, Guid> renewalRequestRepository,
        IRepository<TrackAccountSubscription, Guid> subscriptionRepository,
        IRepository<SmsBundle, Guid> smsBundleRepository,
        ITrackAccountRepository trackAccountRepository)
    {
        _renewalRequestRepository = renewalRequestRepository;
        _subscriptionRepository = subscriptionRepository;
        _smsBundleRepository = smsBundleRepository;
        _trackAccountRepository = trackAccountRepository;
    }

    public async Task<SmsBundleRenewalRequest> CreateAsync(Guid ownerId, Guid subscriptionId, Guid smsBundleId, Guid trackAccountId)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);

        var smsBundleRenewalRequest = await _renewalRequestRepository.AnyAsync(request =>
            request.TrackAccountSubscriptionId == subscriptionId
            && request.SmsBundleRenewalStage == SmsBundleRenewalStage.Payment
            && request.Status != RequestStatus.Canceled
        );

        if (smsBundleRenewalRequest)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateSmsBundleRenewalRequest);
        }

        await _smsBundleRepository.GetAsync(smsBundleId);

        var subscription = await _subscriptionRepository.GetAsync(subscriptionId);
        if (subscription.State != TrackAccountSubscriptionState.Active)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InactiveSubscription);
        }

        var renewalRequest = new SmsBundleRenewalRequest(GuidGenerator.Create(), ownerId, subscriptionId, smsBundleId, trackAccountId);

        var billId = await BillManager.CreateBillAsync(await GenerateBillPlan(renewalRequest),true);
        renewalRequest.SetBillId(billId);

        await _renewalRequestRepository.InsertAsync(renewalRequest, true);

        return renewalRequest;
    }

    public async Task MakeStatusAsReadyToComplete(Guid requestId)
    {
        var request = await _renewalRequestRepository.GetAsync(requestId);

        var subscription = await _subscriptionRepository.GetAsync(request.TrackAccountSubscriptionId);

        var smsBundle = await _smsBundleRepository.GetAsync(bundle => bundle.Id == request.SmsBundleId);
        subscription.IncreaseSmsBundleCount(smsBundle.MessagesCount);

        request.MarkAsReadyForCompletion();

        await _subscriptionRepository.UpdateAsync(subscription);
        await _renewalRequestRepository.UpdateAsync(request);
    }

    public SmsBundleRenewalRequest CreateTemp(Guid ownerId, Guid subscriptionId, Guid smsBundleId, Guid trackAccountId)
    {
        return new SmsBundleRenewalRequest(GuidGenerator.Create(), ownerId, subscriptionId, smsBundleId, trackAccountId);
    }

    public async Task HandlePaymentCompletionAsync(Guid requestId)
    {
        await MakeStatusAsReadyToComplete(requestId);
    }

    public async Task<BillPlan> GenerateBillPlan(SmsBundleRenewalRequest request)
    {
        var remainingMonths = await TrackAccountSubscriptionManager.GetRemainingMonths(request.TrackAccountSubscriptionId);

        return await SmsBundleRenewalRequestBillPlanFactory.GenerateBillPlan(
            new SmsBundleRenewalRequestBillPlanInput(
                request.Id,
                request.OwnerId,
                request.SmsBundleId,
                remainingMonths
            )
        );
    }
}