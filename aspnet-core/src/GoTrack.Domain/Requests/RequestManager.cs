using System;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests;

public class RequestManager : DomainService
{
    private readonly IRepository<Request, Guid> _requestRepository;
    private readonly IRepository<Bill, Guid> _billRepository;
    private readonly RequestPaymentCompletionHandlerFactory _handlerFactory;
    protected BillManager BillManager
        => LazyServiceProvider.LazyGetRequiredService<BillManager>();
    
    public RequestManager(
        IRepository<Request, Guid> requestRepository,
        IRepository<Bill, Guid> billRepository, 
        RequestPaymentCompletionHandlerFactory handlerFactory)
    {
        _requestRepository = requestRepository;
        _billRepository = billRepository;
        _handlerFactory = handlerFactory;
    }

    public async Task CancelAsync(Guid id)
    {
        var request = await _requestRepository.GetAsync(id);
        request.MarkAsCanceled();

        switch (request.Type)
        {
            case RequestType.BusinessAccountSubscription
                when request is BusinessAccountSubscriptionRequest businessRequest:
                businessRequest.SetStageAsFinish();
                break;

            case RequestType.PersonalAccountSubscription
                when request is PersonalAccountSubscriptionRequest personalRequest:
                personalRequest.SetStageAsFinish();
                break;
        }

        await _requestRepository.UpdateAsync(request);
    }

    public async Task RejectAsync(Guid id, string rejectReason)
    {
        var request = await _requestRepository.GetAsync(id);
        request.MarkAsRejected(rejectReason);

        switch (request.Type)
        {
            case RequestType.BusinessAccountSubscription
                when request is BusinessAccountSubscriptionRequest businessRequest:
                businessRequest.SetStageAsFinish();
                break;

            case RequestType.PersonalAccountSubscription
                when request is PersonalAccountSubscriptionRequest personalRequest:
                personalRequest.SetStageAsFinish();
                break;
            
              case RequestType.RenewSubscription
                when request is RenewSubscriptionRequest renewRequest:
                renewRequest.SetStageAsFinish();
                break;

            default:
                throw new BusinessException("InvalidRequestType"); //TODO
        }
        
        if (request is IBillableRequest billableRequest && billableRequest.BillId is not null)
        {
            var bill = await _billRepository.GetAsync(billableRequest.BillId.Value);
            bill.Reject();
            await _billRepository.UpdateAsync(bill);
        }
        
        await _requestRepository.UpdateAsync(request);
    }
    
    public async Task ProcessPaymentAsync(
        Request request,
        PaymentMethod paymentMethod,
        string? note = null)
    {
        if (request is not IBillableRequest billableRequest)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.RequestDoesNotSupportPayments);
        }
        
        if (note is not null)
        {
            request.AddNote(new RequestNote(GuidGenerator.Create(),request.Id, note));
        }

        if (billableRequest.BillId is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillNotAssigned);
        }
        
        await BillManager.MarkBillAsPaidAsync(billableRequest.BillId.Value,paymentMethod,Clock.Now);
        
        var handler = _handlerFactory.GetHandler(request.Type);
        await handler.HandlePaymentCompletionAsync(request.Id);
    }
}