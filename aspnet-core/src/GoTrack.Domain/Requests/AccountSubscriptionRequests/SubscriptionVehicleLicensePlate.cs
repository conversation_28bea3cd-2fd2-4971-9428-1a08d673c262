using System.Collections.Generic;
using GoTrack.Vehicles.LicensePlates;
using Volo.Abp;
using Volo.Abp.Domain.Values;

namespace GoTrack.Requests.AccountSubscriptionRequests;

public class SubscriptionVehicleLicensePlate : ValueObject
{
    public VehicleLicensePlateSubClass SubClass { get; private set; }
    public string Serial { get; private set; }

    public SubscriptionVehicleLicensePlate(VehicleLicensePlateSubClass subClass, string serial)
    {
        SubClass = subClass;
        Serial = Check.NotNullOrEmpty(serial, nameof(serial));
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return SubClass;
        yield return Serial;
    }
}