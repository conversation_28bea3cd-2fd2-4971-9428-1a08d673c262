using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Addresses;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public interface IBusinessAccountSubscriptionRequestManager : IBillableRequestManager<BusinessAccountSubscriptionRequest>
{
    Task<Guid> CreateAsync(Guid ownerId, string companyName, Address address, string accountName,
        TrackerInstallationLocation trackerInstallationLocation, List<SubscriptionVehicleInfo> subscriptionVehicleInfos,
        string subscriptionPlanKey, int userCount, int subscriptionDurationInMonths, string? promoCode,
        Guid? smsBundleId = null);

    Task ApplyDiscountAsync(Guid id, decimal? discountRate, string? note);

    Task InstallDevicesAsync(Guid requestId, List<DeviceInstallationRequest> deviceRequests);

    Task FinishProcessingAsync(Guid id, string? note);
}