using System;
using System.Collections.Generic;
using GoTrack;
using GoTrack.Payments.Bills;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;
using Volo.Abp;

namespace GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;

public class PersonalAccountSubscriptionRequestBillPlanInput : BillPlanInput
{

    public Guid OwnerId { get; private set; }
    public SubscriptionPlanDefinition SubscriptionPlanDefinition { get; private set; }
    public int SubscriptionDurationInMonths { get; private set; }
    public int DevicesCount { get; private set; }
    public int TrackVehiclesCount { get; private set; }
    public Guid? SmsBundleId { get; private set; }
    public string? PromoCode { get; private set; }

    public PersonalAccountSubscriptionRequestBillPlanInput(
        Guid requestId,
        Guid ownerId,
        SubscriptionPlanDefinition subscriptionPlanDefinition,
        int subscriptionDurationInMonths,
        int devicesCount,
        int trackVehiclesCount,
        Guid? smsBundleId,
        string? promoCode = null)
        : base(requestId)
    {
        OwnerId = ownerId;
        SubscriptionPlanDefinition = subscriptionPlanDefinition;
        SubscriptionDurationInMonths = subscriptionDurationInMonths;
        DevicesCount = devicesCount;
        TrackVehiclesCount = trackVehiclesCount;
        SmsBundleId = smsBundleId;
        PromoCode = promoCode;
        Validate();
    }

    protected sealed override void Validate()
    {
        base.Validate();

        if (OwnerId == Guid.Empty)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.OwnerIdRequired);
        }

        if (SubscriptionPlanDefinition.RelatedSubscriptionDurationMonths is not null &&
            SubscriptionPlanDefinition.RelatedSubscriptionDurationMonths!.Contains(SubscriptionDurationInMonths) is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionDurationRelation);
        }

        if (SubscriptionDurationInMonths is < 1 or > 12)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionDuration);
        }
        

        if (DevicesCount < 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DevicesCountNegative);
        }

        if (TrackVehiclesCount < 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.TrackVehiclesCountNegative);
        }

        if (DevicesCount > TrackVehiclesCount)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DevicesCountExceedsVehiclesCount);
        }
    }
}