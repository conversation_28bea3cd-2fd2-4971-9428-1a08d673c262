using GoTrack.Payments.Bills;
using GoTrack.Requests.AccountSubscriptionRequests;
using System;
using System.Collections.Generic;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Volo.Abp;

namespace GoTrack.Requests.AddVehiclesRequests;

public class AddVehiclesRequest : TrackAccountRequest, IBillableRequest, IPromoCodeRequest
{
    public Guid TrackAccountSubscriptionId { get; private set; }
    public List<SubscriptionVehicleInfo> TrackVehicles { get; private set; }
    public TrackerInstallationLocation TrackerInstallationLocation { get; private set; }
    public AddVehiclesRequestStage AddVehiclesRequestStage { get; private set; }
    public bool HasValidDevice { get; private set; }
    public Guid? BillId { get; set; }
    public string? PromoCode { get; private set; }

    #region navigation
    public Bill? Bill { get; set; }
    public TrackAccountSubscription TrackAccountSubscription { get; private set; }
    #endregion

    private AddVehiclesRequest() : base()
    {
        TrackVehicles = [];
    }

    public AddVehiclesRequest(
        Guid id,
        Guid ownerId,
        List<SubscriptionVehicleInfo> trackVehicles,
        TrackerInstallationLocation trackerInstallationLocation,
        Guid trackAccountId,
        bool hasValidDevice, Guid trackAccountSubscriptionId, string? promoCode)
        : base(id, ownerId, RequestType.AddVehiclesRequest, RequestStatus.Pending, trackAccountId)
    {
        TrackVehicles = SubscriptionVehicleInfo.CheckTrackVehiclesForLicensePlateDuplicates(trackVehicles);
        TrackerInstallationLocation = trackerInstallationLocation;
        HasValidDevice = hasValidDevice;
        TrackAccountSubscriptionId = trackAccountSubscriptionId;
        AddVehiclesRequestStage = AddVehiclesRequestStage.Payment;
        PromoCode = promoCode;
    }

    internal void SetStageAsFinish()
    {
        if (AddVehiclesRequestStage is not AddVehiclesRequestStage.DeviceInstallation)
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);

        AddVehiclesRequestStage = AddVehiclesRequestStage.Finished;
    }

    internal void SetStageAsPaymentReview()
    {
        if (AddVehiclesRequestStage is not AddVehiclesRequestStage.Payment)
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage); // TODO

        StartProcessing();
        AddVehiclesRequestStage = AddVehiclesRequestStage.PaymentReview;
    }

    internal void SetStageAsDeviceInstallation()
    {
        if (AddVehiclesRequestStage is not AddVehiclesRequestStage.PaymentReview)
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidSubscriptionStage);

        AddVehiclesRequestStage = AddVehiclesRequestStage.DeviceInstallation;
    }

    public void SetBillId(Guid billId)
    {
        if (BillId is not null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillAlreadyAssigned);
        }

        BillId = billId;
    }
}