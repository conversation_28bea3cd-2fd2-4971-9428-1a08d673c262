using GoTrack.Payments.Discounts;
using GoTrack.SmsBundles;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Payments.PricingItems;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Timing;

namespace GoTrack.Data;

public class DataSeedContributor : IDataSeedContributor, ITransientDependency
{
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepository;
    private readonly IDataFilter _dataFilter;
    private readonly IClock _clock;
    private readonly PricingManager _pricingManager;
    private readonly DiscountManager _discountManager;
    private readonly IRepository<Discount, Guid> _discountRepository;
    private readonly GeoNodeSeeder _geoNodeSeeder;
    private readonly IGuidGenerator _guidGenerator;
    
    public DataSeedContributor(IRepository<SmsBundle, Guid> smsBundleRepository, IDataFilter dataFilter, IClock clock,
        PricingManager pricingManager, DiscountManager discountManager, IRepository<Discount, Guid> discountRepository,
        GeoNodeSeeder geoNodeSeeder, IGuidGenerator guidGenerator)
    {
        _smsBundleRepository = smsBundleRepository;
        _dataFilter = dataFilter;
        _clock = clock;
        _pricingManager = pricingManager;
        _discountManager = discountManager;
        _discountRepository = discountRepository;
        _geoNodeSeeder = geoNodeSeeder;
        _guidGenerator = guidGenerator;
    }


    public async Task SeedAsync(DataSeedContext context)
    {
        if (context.TenantId is not null)
        {
            return;
        }

        using var _ = _dataFilter.Disable<IHaveTrackAccount>();

        await _geoNodeSeeder.SeedAsync();
        await SeedItemPrice();
        await SeedDiscountAsync();
        await SeedSmsBundle();
    }


    private async Task SeedItemPrice()
    {
        await _pricingManager.SetPriceAsync(PricingItemKeys.Device, 850_000);
        await _pricingManager.SetPriceAsync(PricingItemKeys.DeviceInstallation, 0);
        await _pricingManager.SetPriceAsync(PricingItemKeys.AdditionalUsers, 0);
        await _pricingManager.SetPriceAsync(SubscriptionPlanKeys.Silver, 10_000);
        await _pricingManager.SetPriceAsync(SubscriptionPlanKeys.Gold, 15_000);
        await _pricingManager.SetPriceAsync(SubscriptionPlanKeys.Platinum, 20_000);
        await _pricingManager.SetPriceAsync(SubscriptionPlanKeys.PlatinumTrial, 10_000);
    }

    private async Task SeedSmsBundle()
    {
        if (await _smsBundleRepository.AnyAsync())
        {
            return;
        }


        await _smsBundleRepository.InsertManyAsync(new List<SmsBundle>
        {
            new(_guidGenerator.Create(), "الأولى", 100, 500),
            new(_guidGenerator.Create(), "الثانية", 400, 1300),
            new(_guidGenerator.Create(), "الثالثة", 1000, 3000),
            new(_guidGenerator.Create(), "الرابعة", 3000, 7500)
        });
    }


    private async Task SeedDiscountAsync()
    {
        if (await _discountRepository.AnyAsync())
        {
            return;
        }

        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.05m,
            true,
            _clock.Now,
            null,
            6
        );

        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.05m,
            true,
            _clock.Now,
            null,
            7
        );
        
        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.05m,
            true,
            _clock.Now,
            null,
            8
        );
        
        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.05m,
            true,
            _clock.Now,
            null,
            9
        );

        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.05m,
            true,
            _clock.Now,
            null,
            10
        );

        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.05m,
            true,
            _clock.Now,
            null,
            11
        );

        await _discountManager.CreateSubscriptionDurationDiscount(
            _guidGenerator.Create(),
            0.1m,
            true,
            _clock.Now,
            null,
            12
        );
    }
}