using System;
using System.Linq.Expressions;
using Volo.Abp.Specifications;

namespace GoTrack.AlertDefinitions;

public class AlertDefinitionSpecification : Specification<AlertDefinition>
{
    private readonly AlertType _type;

    public AlertDefinitionSpecification(AlertType type)
    {
        _type = type;
    }

    public override Expression<Func<AlertDefinition, bool>> ToExpression()
    {
        return r => r.Type == _type;
    }
}