using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.GeoNodes;

public class GeoNodeManager : DomainService, IGeoNodeManager
{
    private readonly IRepository<GeoNode, Guid> _geoNodeRepository;

    public GeoNodeManager(IRepository<GeoNode, Guid> geoNodeRepository)
    {
        _geoNodeRepository = geoNodeRepository;
    }

    public async Task VerifyAddressAsync(string country, string governorate, string city)
    {
        try
        {
            var countryNode = await _geoNodeRepository.GetAsync(i => i.Name == country);

            var fullTree = await _geoNodeRepository.GetListAsync(i => i.TreeId == countryNode.TreeId);

            var cityNode = fullTree.Single(i => i.NodeType == GeoNodeType.City && i.Name == city);

            var governorateNode = fullTree.Single(i => i.NodeType == GeoNodeType.Governorate && i.Name == governorate);

            var isValidAddress = cityNode.ParentNodeId == governorateNode.Id &&
                                 governorateNode.ParentNodeId == countryNode.Id;

            if (! isValidAddress)
                throw new BusinessException(GoTrackDomainErrorCodes.AddressEnteredIsInvalid);
        }
        catch (Exception)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.AddressEnteredIsInvalid);
        }
    }


    public GeoNode CreateRootNode(string name)
    {
        return new GeoNode(GuidGenerator.Create(), name, GuidGenerator.Create(), GeoNodeType.Country, null);
    }

    public async Task<GeoNode> CreateChildNodeAsync(Guid parentId, string childName, GeoNodeType geoNodeType)
    {
        var parentNode = await _geoNodeRepository.FindAsync(parentId)
                         ?? throw new BusinessException(GoTrackDomainErrorCodes.ParentGeoNodeNotFound);

        ValidateParentChild(parentNode.NodeType, geoNodeType);
        return new GeoNode(GuidGenerator.Create(), childName, parentNode.TreeId, geoNodeType, parentNode.Id);
    }

    private static void ValidateParentChild(GeoNodeType parentNodeType, GeoNodeType childNodeType)
    {
        if (childNodeType == GeoNodeType.Country)
            throw new BusinessException(GoTrackDomainErrorCodes.ChildGeoNodeTypeShouldNotBeCountry);

        if (childNodeType == GeoNodeType.Governorate && parentNodeType != GeoNodeType.Country)
            throw new BusinessException(GoTrackDomainErrorCodes.ParentGeoNodeShouldBeCountry);

        if (childNodeType == GeoNodeType.City && parentNodeType != GeoNodeType.Governorate)
            throw new BusinessException(GoTrackDomainErrorCodes.ParentGeoNodeShouldBeGovernorate);

        if (childNodeType == GeoNodeType.Area && parentNodeType != GeoNodeType.City)
            throw new BusinessException(GoTrackDomainErrorCodes.ParentGeoNodeShouldBeCity);
    }
}