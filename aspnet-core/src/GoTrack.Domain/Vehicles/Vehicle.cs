using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using GoTrack.TrackAccounts;
using GoTrack.Vehicles.LicensePlates;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Vehicles;

public class Vehicle : FullAuditedAggregateRoot<Guid>, IHaveTrackAccount
{
    public Color Color { get; set; }
    public LicensePlate LicensePlate { get; private set; }

    public Guid TrackAccountId { get; private set; }
    
    public double ConsumptionRate { get; private set; }

    // public VehicleSpecification Specification { get; internal set; } 
    public IReadOnlyCollection<VehicleGroupVehicle> VehicleGroupVehicles => _vehicleGroupVehicles.AsReadOnly();
    private readonly List<VehicleGroupVehicle> _vehicleGroupVehicles;
    public TrackAccount TrackAccount { get; set; }
    
    private Vehicle()
    {
        _vehicleGroupVehicles = new List<VehicleGroupVehicle>();
    }

    internal Vehicle(Guid id, TrackAccount trackAccount, LicensePlate licensePlate, Color color, double consumptionRate) : base(id)
    {
        LicensePlate = licensePlate;
        TrackAccountId = trackAccount.Id;
        Color = color;
        _vehicleGroupVehicles = new List<VehicleGroupVehicle>();
    }

    public void Update(LicensePlate licensePlate, Color color)
    {
        LicensePlate = licensePlate;
        Color = color;
    }

    public void UpdateConsumptionRate(double consumptionRate)
    {
        ConsumptionRate = consumptionRate;
    }

    public void RemoveFromGroup(Guid vehicleGroupId)
    {
        var vehicleGroupVehicle = _vehicleGroupVehicles
                               .SingleOrDefault(vg => vg.VehicleGroupId == vehicleGroupId)
                           ?? throw new BusinessException(GoTrackDomainErrorCodes.VehicleNotInGroup);
        _vehicleGroupVehicles.Remove(vehicleGroupVehicle);
    }

    public override bool Equals(object obj)
    {
        if (obj is not Vehicle other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        return Id == other.Id &&
               Color.Equals(other.Color) &&
               TrackAccountId == other.TrackAccountId &&
               ConsumptionRate.Equals(other.ConsumptionRate) &&
               Equals(LicensePlate, other.LicensePlate) &&
               _vehicleGroupVehicles.SequenceEqual(other._vehicleGroupVehicles);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(
            Id,
            Color,
            TrackAccountId,
            ConsumptionRate,
            LicensePlate,
            _vehicleGroupVehicles.Aggregate(0, (hash, item) => HashCode.Combine(hash, item.GetHashCode()))
        );
    }

}