using System.Threading.Tasks;
using GoTrack.Localization;
using Microsoft.Extensions.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Sms;

namespace GoTrack.SMSMessages;

public class SMSEtoEventHandler : IDistributedEventHandler<SendOtpEto>, ITransientDependency
{
    private readonly ISmsSender _smsSender;
    private readonly IStringLocalizer<GoTrackResource> _l;

    public SMSEtoEventHandler(ISmsSender smsSender, IStringLocalizer<GoTrackResource> l)
    {
        _smsSender = smsSender;
        _l = l;
    }

    public async Task HandleEventAsync(SendOtpEto eventData)
    {
        await _smsSender.SendAsync(
            eventData.PhoneNumber,
            _l["OtpMessage", eventData.Otp]
        );
    }
}