using GoTrack.VehicleGroups;
using System;

namespace GoTrack.Observations;

public class ObservationVehicleGroup : Observation
{
    public Guid VehicleGroupId { get; private set; }
    public VehicleGroup VehicleGroup { get; private set; }

    private ObservationVehicleGroup() : base() { }

    public ObservationVehicleGroup(Guid id, Guid userTrackAccountAssociationId, Guid vehicleGroupId) : base(id, userTrackAccountAssociationId, ObservationType.ObserverVehicleGroup)
    {
        VehicleGroupId = vehicleGroupId;
    }
}
