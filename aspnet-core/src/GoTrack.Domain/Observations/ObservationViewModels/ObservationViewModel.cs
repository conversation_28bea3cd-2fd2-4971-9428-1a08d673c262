using GoTrack.Msisdns;
using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Observations.ObservationViewModels;

public class ObservationViewModel : FullAuditedEntity<Guid>
{
    public Guid TrackAccountId { get; private set; }
    public Msisdn PhoneNumber { get; private set; }
    public string ObserverName { get; private set; }
    public string VehicleOrVehicleGroupName { get; private set; }
    public Guid VehicleOrGroupId { get; private set; }
    public ObservationType ObservationType { get; private set; }

    public ObservationViewModel(Guid id,
            Guid? creatorId,
            DateTime creationTime,
            Guid? deleterId,
            DateTime? deletionTime,
            bool isDeleted,
            Guid? lastModifierId,
            DateTime? lastModificationTime,
            Guid trackAccountId,
            Msisdn phoneNumber,
            string vehicleOrVehicleGroupName,
            Guid vehicleOrGroupId,
            ObservationType observationType,
            string observerName)
        : base(id)
    {
        CreatorId = creatorId;
        CreationTime = creationTime;
        DeleterId = deleterId;
        DeletionTime = deletionTime;
        IsDeleted = isDeleted;
        LastModifierId = lastModifierId;
        LastModificationTime = lastModificationTime;

        TrackAccountId = trackAccountId;
        PhoneNumber = phoneNumber;
        VehicleOrVehicleGroupName = vehicleOrVehicleGroupName;
        VehicleOrGroupId = vehicleOrGroupId;
        ObservationType = observationType;
        ObserverName = observerName;
    }
}
