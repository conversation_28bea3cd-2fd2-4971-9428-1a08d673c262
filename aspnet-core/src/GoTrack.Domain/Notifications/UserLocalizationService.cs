using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Notifications;

public class UserLocalizationService : DomainService
{
    private readonly IRepository<IdentityUserProfile, Guid> _identityUserProfileRepository;

    public UserLocalizationService(IRepository<IdentityUserProfile, Guid> identityUserProfileRepository)
    {
        _identityUserProfileRepository = identityUserProfileRepository;
    }

    /// <summary>
    /// Gets the preferred language for a specific user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>The user's preferred language code (e.g., "ar", "en")</returns>
    public async Task<string> GetUserPreferredLanguageAsync(Guid userId)
    {
        var userProfile = await _identityUserProfileRepository.FindAsync(p => p.UserId == userId);
        
        if (userProfile != null && !string.IsNullOrWhiteSpace(userProfile.PreferredLanguage))
        {
            return userProfile.PreferredLanguage;
        }

        return SupportedLanguageExtensions.DefaultLanguage;
    }

    /// <summary>
    /// Gets preferred languages for multiple users
    /// </summary>
    /// <param name="userIds">List of user IDs</param>
    /// <returns>Dictionary mapping user ID to preferred language</returns>
    public async Task<Dictionary<Guid, string>> GetUsersPreferredLanguagesAsync(List<Guid> userIds)
    {
        if (!userIds.Any())
            return new Dictionary<Guid, string>();

        var userProfiles = await _identityUserProfileRepository.GetListAsync(p => userIds.Contains(p.UserId));
        
        var result = new Dictionary<Guid, string>();
        
        foreach (var userId in userIds)
        {
            var userProfile = userProfiles.FirstOrDefault(p => p.UserId == userId);
            var preferredLanguage = userProfile?.PreferredLanguage;
            
            if (string.IsNullOrWhiteSpace(preferredLanguage))
            {
                preferredLanguage = SupportedLanguageExtensions.DefaultLanguage;
            }
            
            result[userId] = preferredLanguage;
        }

        return result;
    }

    /// <summary>
    /// Sets the current thread's culture to the user's preferred language
    /// </summary>
    /// <param name="userId">The user ID</param>
    public async Task SetCurrentCultureForUserAsync(Guid userId)
    {
        var preferredLanguage = await GetUserPreferredLanguageAsync(userId);
        SetCurrentCulture(preferredLanguage);
    }

    /// <summary>
    /// Sets the current thread's culture to the specified language
    /// </summary>
    /// <param name="languageCode">The language code (e.g., "ar", "en")</param>
    public void SetCurrentCulture(string languageCode)
    {
        try
        {
            var cultureInfo = new CultureInfo(languageCode);
            CultureInfo.CurrentUICulture = cultureInfo;
        }
        catch (CultureNotFoundException ex)
        {
            var defaultCultureInfo = new CultureInfo(SupportedLanguageExtensions.DefaultLanguage);
            CultureInfo.CurrentUICulture = defaultCultureInfo;
        }
    }

    /// <summary>
    /// Groups users by their preferred language
    /// </summary>
    /// <param name="userIds">List of user IDs</param>
    /// <returns>Dictionary mapping language code to list of user IDs</returns>
    public async Task<Dictionary<string, List<Guid>>> GroupUsersByLanguageAsync(List<Guid> userIds)
    {
        var userLanguages = await GetUsersPreferredLanguagesAsync(userIds);
        
        return userLanguages
            .GroupBy(kvp => kvp.Value)
            .ToDictionary(
                group => group.Key,
                group => group.Select(kvp => kvp.Key).ToList()
            );
    }
}
