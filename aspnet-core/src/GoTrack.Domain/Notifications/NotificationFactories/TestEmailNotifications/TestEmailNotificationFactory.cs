using Notify;
using Notify.Provider.Email;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace GoTrack.Notifications.NotificationFactories.TestEmailNotifications;

public class TestEmailNotificationFactory : NotificationFactory<TestEmailNotificationDataModel, CreateEmailNotificationEto>, ITransientDependency
{
    public override Task<CreateEmailNotificationEto> CreateAsync(TestEmailNotificationDataModel model, IEnumerable<Guid> userIds)
    {
        return Task.FromResult(new CreateEmailNotificationEto(userIds, model.Subject, model.Body));
    }
}
