using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PricingItems;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Payments.PromoCodes;

public class PromoCodeManager : DomainService
{
    private readonly IRepository<PromoCode, Guid> _promoCodeRepository;
    private readonly ILogger<PromoCodeManager> _logger;
    private readonly IRepository<Discount, Guid> _discountRepository;

    protected DiscountManager DiscountManager =>
        LazyServiceProvider.LazyGetRequiredService<DiscountManager>();

    public PromoCodeManager(
        IRepository<PromoCode, Guid> promoCodeRepository,
        ILogger<PromoCodeManager> logger,
        IRepository<Discount, Guid> discountRepository)
    {
        _promoCodeRepository = promoCodeRepository;
        _logger = logger;
        _discountRepository = discountRepository;
    }

    public async Task<PromoCode> CreatePromoCodeAsync(
        string code,
        decimal discountValue,
        DateTime dateFrom,
        DateTime dateTo)
    {
        code = code.ToUpper();

        if (dateFrom.Date < Clock.Now.Date)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeStartDateMustBeInFuture);
        }

        if (dateTo.Date < Clock.Now.Date)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeEndDateMustBeInFuture);
        }

        var overlappingPromoCode = await _promoCodeRepository.FirstOrDefaultAsync(p =>
            p.Code == code &&
            p.DateTo >= dateFrom);

        if (overlappingPromoCode != null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeAlreadyExists);
        }

        var discountCriteria = new List<DiscountCriteriaViewModel>
        {
            new DiscountCriteriaViewModel(DiscountSpecificationKey.PromoCode, code)
        };

        var discount = await DiscountManager.CreateDiscountAsync(
            GuidGenerator.Create(),
            $"Promo Code: {code}",
            TargetType.OnListItem,
            discountValue,
            true,
            dateFrom,
            discountCriteria,
            [PricingItemKeys.Device],
            dateTo
        );

        var promoCode = new PromoCode(
            GuidGenerator.Create(),
            code,
            discount.Id,
            dateFrom,
            dateTo
        );

        await _promoCodeRepository.InsertAsync(promoCode);

        return promoCode;
    }

    public async Task<PromoCode> GetPromoCodeAsync(string code)
    {
        var promoCode = await _promoCodeRepository.FirstOrDefaultAsync(p => p.Code == code);

        if (promoCode == null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeNotFound);
        }

        return promoCode;
    }

    public async Task<List<PromoCode>> GetPromoCodesAsync(int skipCount, int maxResultCount, string filter = null)
    {
        var query = await _promoCodeRepository.GetQueryableAsync();

        if (!string.IsNullOrWhiteSpace(filter))
        {
            query = query.Where(p => p.Code.Contains(filter));
        }

        return await AsyncExecuter.ToListAsync(
            query.OrderByDescending(p => p.CreationTime)
                .Skip(skipCount)
                .Take(maxResultCount)
        );
    }


    public bool IsCurrentlyActive(PromoCode promoCode)
    {
        var now = Clock.Now;
        return now >= promoCode.DateFrom && now <= promoCode.DateTo;
    }

    public async Task UpdatePromoCodeRangeAsync(Guid id, DateTime newStartDate, DateTime newEndDate)
    {
        var queryable = await _promoCodeRepository.WithDetailsAsync(x => x.Discount);

        var promoCode = await AsyncExecuter.FirstOrDefaultAsync(queryable.Where(x => x.Id == id));

        if (promoCode is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeNotFound);
        }

        if (newStartDate.Date < Clock.Now.Date)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeStartDateMustBeInFuture);
        }
    
        if (newEndDate.Date < Clock.Now.Date)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeEndDateMustBeInFuture);
        }

        if (newEndDate <= newStartDate)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidDateRange);
        }

        promoCode.UpdateDate(newStartDate, newEndDate);
        promoCode.Discount.UpdateDate(newStartDate, newEndDate);
        await _promoCodeRepository.UpdateAsync(promoCode);
        await _discountRepository.UpdateAsync(promoCode.Discount);
    }

    public async Task DeactivatePromoCodeAsync(Guid id)
    {
        var queryable = await _promoCodeRepository.WithDetailsAsync(x => x.Discount);

        var promoCode = await AsyncExecuter.FirstOrDefaultAsync(queryable.Where(x => x.Id == id));

        if (promoCode is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeNotFound);
        }

        promoCode.SetDateTo(Clock.Now);
        promoCode.Discount.SetEndDate(Clock.Now);

        await _promoCodeRepository.UpdateAsync(promoCode);
        await _discountRepository.UpdateAsync(promoCode.Discount);
    }

    public async Task<bool> ValidatePromoCodeAsync(string code)
    {
        code = code.ToUpper();

        var promoCode = await _promoCodeRepository.FirstOrDefaultAsync(p => p.Code == code);

        if (promoCode == null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeNotFound);
        }

        if (!IsCurrentlyActive(promoCode))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PromoCodeExpired);
        }

        return true;
    }
}