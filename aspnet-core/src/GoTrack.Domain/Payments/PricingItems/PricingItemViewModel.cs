using System;
using System.Collections.Generic;
using Volo.Abp.Localization;

namespace GoTrack.Payments.PricingItems;

public class PricingItemViewModel
{
    public string Key { get; set; } = null!;
    public ILocalizableString DisplayName { get; set; } = null!;
    public PricingType PricingType { get; set; }
    public decimal CurrentPrice { get; set; } = -1;
    public IReadOnlyList<Price> LastPriceChanges { get; set; } = [];
    public DateTime LastPriceChangeDate { get; set; }
}