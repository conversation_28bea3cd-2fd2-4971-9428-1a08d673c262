using System;

namespace GoTrack.Alerts.Balancer.Models;

public class Round
{
    public int RoundId { get; set; }
    public decimal ServerServiceId { get; set; }
    public decimal ServiceId { get; set; }
    public decimal ServerId { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime EndedAt { get; set; }
    public decimal DefinedAlerts { get; set; }
    public decimal HandledAlerts { get; set; }
    public DateTime CreationDate { get; set; }

    public virtual Server Server { get; set; }
    public virtual Service Service { get; set; }
}