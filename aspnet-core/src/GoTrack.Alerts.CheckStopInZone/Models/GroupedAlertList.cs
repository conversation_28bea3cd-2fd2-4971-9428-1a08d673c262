using GoTrack.Alerts.BaseChecker.Models;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.Threading.Tasks;
using Warp10Abstraction.Models;
using Warp10Abstraction.WarpLibs;

namespace GoTrack.Alerts.CheckStopInZone.Models;

public class GroupedAlertList : GroupedAlertListBase
{
    public string Polygon { get; set; }

    public override async Task<ViolationResult[]> CheckAlert(
        IWarpLib warpLib,
        string fromDate,
        string toDate,
        IConfiguration configuration)
    {
        var result = await warpLib.CheckStopInZone(
            Alerts.Select(x => x.Imei).First(),
            60,
            10,
            5,
            80,
            Polygon,
            fromDate,
            toDate
        );

        result.Imei = Alerts.Select(x => x.Imei).First();

        return [result];
    }
}