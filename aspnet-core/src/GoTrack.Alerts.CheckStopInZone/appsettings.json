{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=GoTrack_Alerts_Check_Stop_In_Zone;Uid=root;Pwd=;TreatTinyAsBoolean=true;Connection Timeout=300;default command timeout=300"}, "Warp10": {"Url": "http://************:8881/api/v0/exec", "ReadToken": "tJU_xH_Lj1u_hHEguyYLkyfIBECCg8P7b.9dLiEjqe2r2VymHSvYwXYwmqXZCovwPw6g1WBoHEZ.7Pe9nUYlyrfJAa7bQ95xUz3udef7HpoVXSEG4DaOYfpD5510yumVpBJpYHdx5XLJk1lmCm_UqDAaL9xCWc8VyhDwS4zErWAWuAIjMoSnn.", "WriteToken": ""}, "InstanceCode": "SZ_01", "RabbitMQ": {"Host": "127.0.0.1", "VirtualHost": "/", "Username": "guest", "Password": "guest"}, "MaxDegreeOfParallelism": 10, "OpenTelemetry": {"EndpointUri": "http://jaeger:4317", "EnableTracing": true, "ApplicationName": "GoTrack_Alert_CheckStopInZone"}}