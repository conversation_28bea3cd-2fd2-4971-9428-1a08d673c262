using Fatora;
using Fatora.Options;
using GoTrack.Email;
using GoTrack.Identity;
using GoTrack.Requests;
using GoTrack.Requests.AddVehiclesRequests;
using GoTrack.Requests.IncreaseUserCountRequests;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Requests.SmsBundleRenewalRequests;
using GoTrack.Requests.TrackAccountRequests;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Notify.EntityFrameworkCore;
using System;
using GoTrack.Infrastructure.OpenTelemetry;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.DependencyInjection;
using Volo.Abp.EntityFrameworkCore.MySQL;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Http.Client;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.MailKit;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace GoTrack.EntityFrameworkCore;

[DependsOn(
    typeof(GoTrackDomainModule),
    typeof(AbpIdentityEntityFrameworkCoreModule),
    typeof(AbpOpenIddictEntityFrameworkCoreModule),
    typeof(AbpPermissionManagementEntityFrameworkCoreModule),
    typeof(AbpSettingManagementEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreMySQLModule),
    typeof(AbpBackgroundJobsEntityFrameworkCoreModule),
    typeof(AbpAuditLoggingEntityFrameworkCoreModule),
    typeof(AbpTenantManagementEntityFrameworkCoreModule),
    typeof(AbpFeatureManagementEntityFrameworkCoreModule),
    typeof(AbpCachingStackExchangeRedisModule),
    typeof(NotifyEntityFrameworkCoreModule),
    typeof(AbpHttpClientModule),
    typeof(BlobStoringDatabaseEntityFrameworkCoreModule))]
public class GoTrackEntityFrameworkCoreModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        GoTrackEfCoreEntityExtensionMappings.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<GoTrackDbContext>(options =>
        {
                /* Remove "includeAllEntities: true" to create
                 * default repositories only for aggregate roots */
            options.AddDefaultRepositories(includeAllEntities: true);
        });
        
        context.Services.Replace(
            ServiceDescriptor
                .Transient<IRepository<TrackAccountRequest, Guid>,
                    TrackAccountFilteredRepository<TrackAccountRequest>>()
        );
        context.Services.Replace(
            ServiceDescriptor
                .Transient<IRepository<IncreaseUserCountRequest, Guid>,
                    TrackAccountFilteredRepository<IncreaseUserCountRequest>>()
        );
        context.Services.Replace(
            ServiceDescriptor
                .Transient<IRepository<AddVehiclesRequest, Guid>,
                    TrackAccountFilteredRepository<AddVehiclesRequest>>()
        );
        context.Services.Replace(
            ServiceDescriptor
                .Transient<IRepository<RenewSubscriptionRequest, Guid>,
                    TrackAccountFilteredRepository<RenewSubscriptionRequest>>()
        );
        context.Services.Replace(
            ServiceDescriptor
                .Transient<IRepository<SmsBundleRenewalRequest, Guid>,
                    TrackAccountFilteredRepository<SmsBundleRenewalRequest>>()
        );


        context.Services.AddDataProtection()
            .PersistKeysToDbContext<GoTrackDbContext>();

        Configure<AbpDbContextOptions>(options =>
        {
                /* The main point to change your DBMS.
                 * See also GoTrackMigrationsDbContextFactory for EF Core tooling. */
            options.UseMySQL(options => options.UseNetTopologySuite());
        });

        Configure<AbpEntityOptions>(options =>
        {
            options.Entity<IdentityUserProfile>(entityOptions =>
            {
                entityOptions.DefaultWithDetailsFunc = profiles => profiles
                    .Include(b => b.User);
            });
        });


        var configuration = context.Services.GetConfiguration();
        var fatoraOptionsSection = configuration.GetSection("FatoraOptions");
        Configure<FatoraOptions>(fatoraOptionsSection);
        //
        // var fatoraOptions = context.Services.BuildServiceProvider().GetService<IOptions<FatoraOptions>>()?.Value ??
        //                           throw new Exception("Cant Get Masstransit rabbitmq exception");

        context.Services.AddFatoraServices(option => 
        {
            option.Url = configuration["Fatora:Url"] ?? "https://egate-t.fatora.me/api/";
            option.UserName = configuration["Fatora:UserName"] ?? "gotrack";
            option.Password = configuration["Fatora:Password"] ?? "gotrack@123";
            option.TerminalId = configuration["Fatora:TerminalId"] ?? "14740084";
            option.MinLogLevel = Enum.Parse<LogLevel>(configuration["Fatora:MinLogLevel"]?.ToString() ?? "6");
        });

        context.Services.AddHttpClient();
        
        context.Services.Replace(ServiceDescriptor.Transient<IMailKitSmtpEmailSender, CustomMailKitSmtpEmailSender>());

        //context.Services.AddTransient<IUserNotificationRepositoryExtended, UserNotificationRepositoryExtended>();
    }
}