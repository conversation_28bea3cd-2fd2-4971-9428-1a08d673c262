using GoTrack.Requests.AddVehiclesRequests;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Drawing;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Requests.AddVehiclesRequests;

public class AddVehiclesRequestEntityConfiguration : IEntityTypeConfiguration<AddVehiclesRequest>
{
    public void Configure(EntityTypeBuilder<AddVehiclesRequest> builder)
    {
        builder.ConfigureByConvention();

        builder.OwnsMany(r => r.TrackVehicles, t =>
        {
            t.ToGoTrackTable("AddVehiclesRequestTrackVehicles");

            t.OwnsOne(tv => tv.LicensePlate);
            t.Property(v => v.Color).HasConversion(
                c => ColorTranslator.ToWin32(c),
                i => ColorTranslator.FromWin32(i)
            );
        });

        builder.ToGoTrackTable();
    }
}
