using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class PersonalAccountSubscriptionRequest_Address : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Address_Area",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Address_City",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Address_Country",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Address_Governorate",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Address_Street",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Address_Area",
                table: "GoTrackPersonalAccountSubscriptionRequests");

            migrationBuilder.DropColumn(
                name: "Address_City",
                table: "GoTrackPersonalAccountSubscriptionRequests");

            migrationBuilder.DropColumn(
                name: "Address_Country",
                table: "GoTrackPersonalAccountSubscriptionRequests");

            migrationBuilder.DropColumn(
                name: "Address_Governorate",
                table: "GoTrackPersonalAccountSubscriptionRequests");

            migrationBuilder.DropColumn(
                name: "Address_Street",
                table: "GoTrackPersonalAccountSubscriptionRequests");
        }
    }
}
