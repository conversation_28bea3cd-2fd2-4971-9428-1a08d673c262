using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace GoTrack.TrackAccounts;

public class TrackAccountRepository : EfCoreRepository<GoTrackDbContext, TrackAccount, Guid>, ITrackAccountRepository
{
    public TrackAccountRepository(IDbContextProvider<GoTrackDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public async Task<IQueryable<TrackAccount>> GetTrackAccountByUserIdQueryableAsync(Guid userId)
    {
        return (await WithDetailsAsync(x => x.TrackAccountSubscriptions))
            .Include(x => x.UserTrackAccountAssociations.Where(y => y.UserId == userId))
            .Where(x => x.UserTrackAccountAssociations.Any(y => y.UserId == userId))
            .AsQueryable();
    }
}
