using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Drawing;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.StopPoints.EntityConfiguration;

public class StopPointEntityConfiguration : IEntityTypeConfiguration<StopPoint>
{
    public void Configure(EntityTypeBuilder<StopPoint> builder)
    {
        builder.ConfigureByConvention();

        builder.Property(v => v.Color)
            .HasConversion(
                c => ColorTranslator.ToWin32(c),
                i => ColorTranslator.FromWin32(i)
            );

        builder.ToGoTrackTable();
    }
}
