using GoTrack.AlertDefinitions.JobTimeAlertDefinitions;
using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Linq;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.AlertDefinitions.EntityConfiguration.JobTimeAlertDefinitions;

public class JobTimeAlertDefinitionEntityConfiguration : IEntityTypeConfiguration<JobTimeAlertDefinition>
{
    private static readonly char separator = ',';

    public void Configure(EntityTypeBuilder<JobTimeAlertDefinition> builder)
    {
        builder.ConfigureByConvention();

        builder.Property(x => x.DaysOfWeek)
            .HasConversion(
                v => string.Join(separator, v.Select(s => s.ToString())), // Convert enum list to a comma-separated string

                v => v.Split(separator, StringSplitOptions.RemoveEmptyEntries)
                      .Select(s => (DayOfWeek)Enum.Parse(typeof(DayOfWeek), s))
                      .ToList()
            );

        builder.ToGoTrackTable();
    }
}
