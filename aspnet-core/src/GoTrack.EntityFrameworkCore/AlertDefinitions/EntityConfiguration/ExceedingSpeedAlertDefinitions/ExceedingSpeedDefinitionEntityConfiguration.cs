using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.AlertDefinitions.EntityConfiguration.ExceedingSpeedAlertDefinitions;

public class ExceedingSpeedAlertDefinitionEntityConfiguration : IEntityTypeConfiguration<ExceedingSpeedAlertDefinition>
{
    public void Configure(EntityTypeBuilder<ExceedingSpeedAlertDefinition> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}
