using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Notify.EntityFrameworkCore;
using Notify.Notifications.UserNotifications;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.EntityFrameworkCore;

namespace GoTrack.Notifications;

public class UserNotificationRepositoryExtended
    : UserNotificationRepository, IUserNotificationRepositoryExtended
{
    public UserNotificationRepositoryExtended(IDbContextProvider<INotifyDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public async Task<IQueryable<UserNotification>> GetQueryableAsync(Guid trackAccoundId)
    {
        return (await GetQueryableAsync()).Where(x =>
            EF.Property<Guid?>(x, NotifyProviderFCMConstsExtensions.UserNotificationTrackAccoundIdPropertyName) == trackAccoundId ||
            EF.Property<Guid?>(x, NotifyProviderFCMConstsExtensions.UserNotificationTrackAccoundIdPropertyName) == null
        );
    }
}
