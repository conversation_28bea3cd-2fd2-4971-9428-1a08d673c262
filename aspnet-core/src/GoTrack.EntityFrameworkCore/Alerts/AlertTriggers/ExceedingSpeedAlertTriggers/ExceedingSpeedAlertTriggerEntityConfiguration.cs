using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers;

public class ExceedingSpeedAlertTriggerEntityConfiguration : IEntityTypeConfiguration<ExceedingSpeedAlertTrigger>
{
    public void Configure(EntityTypeBuilder<ExceedingSpeedAlertTrigger> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}
