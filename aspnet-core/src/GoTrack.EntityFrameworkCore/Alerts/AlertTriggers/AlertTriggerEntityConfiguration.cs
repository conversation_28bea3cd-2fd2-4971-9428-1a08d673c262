using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Linq;
using System;
using Volo.Abp.EntityFrameworkCore.Modeling;
using GoTrack.AlertDefinitions;

namespace GoTrack.Alerts.AlertTriggers;

public class AlertTriggerEntityConfiguration : IEntityTypeConfiguration<AlertTrigger>
{
    private static readonly char separator = ',';

    public void Configure(EntityTypeBuilder<AlertTrigger> builder)
    {
        builder.ConfigureByConvention();

        builder.UseTptMappingStrategy();

        builder.Property(x => x.NotificationMethods)
            .HasConversion(
                v => string.Join(separator, v.Select(s => s.ToString())), // Convert enum list to a comma-separated string

                v => v.Split(separator, StringSplitOptions.RemoveEmptyEntries)
                      .Select(s => (AlertDefinitionNotificationMethod)Enum.Parse(typeof(AlertDefinitionNotificationMethod), s))
                      .ToList()
            );

        builder.ToGoTrackTable();
    }
}
